import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/models/skill_model.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import 'package:dartz/dartz.dart';

class SkillsSource {
  static Future<Either<String, bool>> addSkill(
    SkillModel skill,
    String userId,
  ) async {
    try {
      final skillMap = skill.toJson();

      final response = await SubabaseServices.addMapToList(
        table: 'portfolio_data',
        fieldName: 'skills',
        mapToAdd: skillMap,
        matchColumn: 'userId',
        matchValue: userId,
      );

      if (response.status) {
        await UserDataService.addItemToList(LocalStorageKeys.skills, skillMap);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> updateSkill(
    SkillModel skill,
    String userId,
  ) async {
    try {
      final skillMap = skill.toJson();
      final currentSkills = UserDataService.getListData(
        LocalStorageKeys.skills,
      );
      final existingSkill = currentSkills.firstWhere(
        (s) => s['id'] == skill.id,
        orElse: () => {},
      );
      if (existingSkill.isEmpty) {
        return Left('Skill not found for update');
      }
      final response = await SubabaseServices.updateMapInList(
        table: 'portfolio_data',
        fieldName: 'skills',
        matchColumn: 'userId',
        matchValue: userId,
        updateByKey: 'id',
        updateByValue: skill.id ?? '',
        newMapData: skillMap,
      );

      if (response.status) {
        await UserDataService.updateItemInList(
          LocalStorageKeys.skills,
          skill.id!,
          skillMap,
        );
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> deleteSkill(
    String id,
    String userId,
  ) async {
    try {
      final response = await SubabaseServices.deleteMapFromList(
        table: 'portfolio_data',
        fieldName: 'skills',
        matchColumn: 'userId',
        matchValue: userId,
        deleteByKey: 'id',
        deleteByValue: id,
      );
      if (response.status) {
        await UserDataService.deleteItemFromList(LocalStorageKeys.skills, id);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, List<SkillModel>>> getSkills({
    bool? refresh,
    String? userId,
  }) async {
    try {
      if (refresh == false || refresh == null) {
        final data = UserDataService.getListData(LocalStorageKeys.skills);
        if (data.isNotEmpty) {
          final skills = data
              .map((skillData) => SkillModel.fromJson(skillData))
              .toList();
          return Right(skills);
        }
      }

      if (userId != null) {
        final response = await SubabaseServices.get(
          table: 'portfolio_data',
          filter: {'userId': userId},
        );

        if (response.status &&
            response.data != null &&
            response.data.isNotEmpty) {
          final portfolioData = response.data[0];
          final skillsList = portfolioData['skills'] as List<dynamic>? ?? [];
          final skills = skillsList
              .map(
                (skillData) =>
                    SkillModel.fromJson(skillData as Map<String, dynamic>),
              )
              .toList();

          // Update local cache
          await UserDataService.updateListData(
            LocalStorageKeys.skills,
            skillsList.map((e) => e as Map<String, dynamic>).toList(),
          );

          return Right(skills);
        }
      }

      return Right([]);
    } catch (e) {
      return Left(e.toString());
    }
  }
}
