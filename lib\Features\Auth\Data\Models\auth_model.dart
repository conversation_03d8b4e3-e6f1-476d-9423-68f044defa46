class AuthModel {
  final String? id;
  final String name;
  final String email;
  final String password;
  final String phone;

  AuthModel({
    this.id,
    required this.name,
    required this.email,
    required this.password,
    required this.phone,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'password': password,
      'phone': phone,
    };
  }

  AuthModel.login({required this.email, required this.password})
    : id = null,
      name = '',
      phone = '';

  Map<String, dynamic> toLogin<PERSON><PERSON>() {
    return {'email': email, 'password': password};
  }

  factory AuthModel.fromJson(Map<String, dynamic> json) {
    return AuthModel(
      id: json['id'],
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      password: json['password'] ?? '',
      phone: json['phone'] ?? '',
    );
  }

  AuthModel copyWith({
    String? id,
    String? name,
    String? email,
    String? password,
    String? phone,
  }) {
    return AuthModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      password: password ?? this.password,
      phone: phone ?? this.phone,
    );
  }
}
