import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConfirmationDialog extends StatelessWidget {
  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    required this.confirmText,
    required this.onConfirm,
    this.cancelText = 'Cancel',
    this.isDestructive = false,
  });

  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  final bool isDestructive;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          ResponsiveLayout.getBorderRadius(context),
        ),
      ),
      title: CustomText(
        text: title,
        fontSize: ResponsiveLayout.getSubtitleFontSize(context),
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      content: CustomText(
        text: message,
        fontSize: ResponsiveLayout.getBodyFontSize(context),
        color: AppColors.textSecondary,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: CustomText(
            text: cancelText,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(width: 10.w),
        CustomButton(
          text: confirmText,
          onPressed: () {
            Navigator.of(context).pop();
            onConfirm();
          },
          backgroundColor: isDestructive ? Colors.red : AppColors.primary,
          width: 100.w,
          height: 40.h,
        ),
      ],
    );
  }

  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    required String confirmText,
    String cancelText = 'Cancel',
    bool isDestructive = false,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        isDestructive: isDestructive,
        onConfirm: () => Navigator.of(context).pop(true),
      ),
    );
  }
}
