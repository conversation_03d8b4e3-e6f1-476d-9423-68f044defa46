
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/Utils/Reusable/custom_text.dart';
import '../../../../../Core/Utils/widgets/header_a_ll.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/skill_model.dart';
import '../../../../../Core/resources/resources.dart';
import '../Widgets/skill_form.dart';
import '../Widgets/skills_empty_state.dart';
import '../Widgets/skills_list.dart';
import '../cubit/skills_cubit.dart';
import '../cubit/skills_state.dart';

class Skillspage extends StatefulWidget {
  const Skillspage({super.key});

  @override
  State<Skillspage> createState() => _SkillspageState();
}

class _SkillspageState extends State<Skillspage> {

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(25.w),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.withOpacity(AppColors.shadowDark, 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HeaderALl(
            title: AppStrings.skillsManagement,
            description: AppStrings.manageSkillsDescription,
            icon: Icons.psychology,
          ),
          SizedBox(height: 25.h),
          Expanded(
            child: BlocConsumer<SkillsCubit, SkillsState>(
              listener: (context, state) {
                if (state.errorMessage != null && state.errorMessage!.isNotEmpty ) {
                  if (state.isSuccess == false) {
                  MessageWidget.show(
                    context,
                      type: MessageType.error,
                      message: state.errorMessage!,
                    );
                  }
                  if (state.isSuccess == true) {
                    MessageWidget.show(
                      context,
                      type: MessageType.success,
                      message: "successfully",
                    );
                  }
                    context.read<SkillsCubit>().resetSuccess();
                  
                }
              },
              builder: (context, state) {
                if (state.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(color: AppColors.primary),
                  );
                }

                return _buildSkillsbody(state);
              },
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.1);
  }

  Widget _buildSkillsbody(SkillsState state) {
    var isMobileOrTablet =
        ResponsiveLayout.isMobile(context) ||
        ResponsiveLayout.isTablet(context);
    return isMobileOrTablet
        ? ListView(
            reverse: true,
            physics: const RangeMaintainingScrollPhysics(),
            children: [
              SizedBox(
                height: 500.h,
                child: state.skillsList.isEmpty
                    ? SkillsEmptyState(
                        onAddSkill: () {
                          context.read<SkillsCubit>().newOrEdit(newSkill: true);
                        },
                      )
                    : _buildSkillsList(state),
              ),
              SizedBox(height: 20.h),
              SizedBox(height: 1000.h, child: _buildSkillForm(state)),
            ],
          )
        : Row(
            children: [
              // Skills List
              Expanded(
                flex: 2,
                child: state.skillsList.isEmpty
                    ? SkillsEmptyState(
                        onAddSkill: () {
                          context.read<SkillsCubit>().newOrEdit(newSkill: true);
                        },
                      )
                    : _buildSkillsList(state),
              ),
              SizedBox(width: 20.w),
              // Add/Edit Form
              Expanded(child: _buildSkillForm(state)),
            ],
          );
  }

  Widget _buildSkillsList(SkillsState state) {
    return SkillsList(
      skills: state.skillsList,
      selectedSkill: state.editingSkill,
      onDelete: (skill) {
        _showDeleteDialog(skill).then((value) {
          if (value == true) {
            if (mounted) {
              context.read<SkillsCubit>().deleteSkill(skill);
            }
          }
        });
      },
      onSelect: (skill) {
        context.read<SkillsCubit>().newOrEdit(
          newSkill: false,
          editingSkill: true,
        );
        context.read<SkillsCubit>().selectSkill(skill);
      },
    );
  }

  Widget _buildSkillForm(SkillsState state) {
    return SkillForm(
      skill: state.editingSkill,
      isAddingNew: state.isAddingNew,
      onSave: (name, category, icon) {
        if (state.isAddingNew) {
          final skill = SkillModel(
            id: SkillModel.generateTimeIdString(),
            name: name,
            category: category,
            icon: icon,
          );
          context.read<SkillsCubit>().addNewSkill(skill);
        } else if (state.isEditing) {
          final skill = state.editingSkill?.copyWith(
            name: name,
            category: category,
            icon: icon,
          );
          context.read<SkillsCubit>().editSkill(skill!);
        }
      },
      onCancel: () {
        if (state.isEditing) {
          context.read<SkillsCubit>().newOrEdit(
            newSkill: true,
            editingSkill: false,
          );
        }
        context.read<SkillsCubit>().selectSkill(SkillModel(id: '', name: '', category: '', icon: ''));
      },
    );
  }

  Future<bool> _showDeleteDialog(SkillModel skill) async {
    return await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        title: CustomText(
          text: AppStrings.deleteSkill,
          fontSize: ResponsiveLayout.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
        ),
        content: CustomText(
          text: '${AppStrings.deleteSkillConfirmation} "${skill.name}"?',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: CustomText(
              text: AppStrings.cancel,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: AppColors.textSecondary,
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context, true);
            },
            child: CustomText(
              text: AppStrings.delete,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }
}
