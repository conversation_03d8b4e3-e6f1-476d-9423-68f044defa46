class EducationModel {
  final String? id;
  final String? degree;
  final String? institution;
  final String? startDate;
  final String? endDate;
  final String? description;
  final String? location;
  final String? gpa;

  EducationModel({
    this.id,
    this.degree,
    this.institution,
    this.startDate,
    this.endDate,
    this.description,
    this.location,
    this.gpa,
  });

  factory EducationModel.fromJson(Map<String, dynamic> json) {
    return EducationModel(
      id: json['id'],
      degree: json['degree'],
      institution: json['institution'],
      startDate: json['startDate'] ?? json['start_date'],
      endDate: json['endDate'] ?? json['end_date'],
      description: json['description'],
      location: json['location'],
      gpa: json['gpa'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (degree != null) 'degree': degree,
      if (institution != null) 'institution': institution,
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (description != null) 'description': description,
      if (location != null) 'location': location,
      if (gpa != null) 'gpa': gpa,
    };
  }

  EducationModel copyWith({
    String? id,
    String? degree,
    String? institution,
    String? startDate,
    String? endDate,
    String? description,
    String? location,
    String? gpa,
  }) {
    return EducationModel(
      id: id ?? this.id,
      degree: degree ?? this.degree,
      institution: institution ?? this.institution,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      description: description ?? this.description,
      location: location ?? this.location,
      gpa: gpa ?? this.gpa,
    );
  }

  static String generateTimeIdString() {
    return (DateTime.now().millisecondsSinceEpoch % 1000000).toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EducationModel &&
        other.id == id &&
        other.degree == degree &&
        other.institution == institution &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.description == description &&
        other.location == location &&
        other.gpa == gpa;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        degree.hashCode ^
        institution.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        description.hashCode ^
        location.hashCode ^
        gpa.hashCode;
  }

  @override
  String toString() {
    return 'EducationModel(id: $id, degree: $degree, institution: $institution, startDate: $startDate, endDate: $endDate, description: $description, location: $location, gpa: $gpa)';
  }
}
