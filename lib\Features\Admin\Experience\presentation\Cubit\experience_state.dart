import '../../../../../Core/models/experience_model.dart';

class ExperienceState {
  final List<ExperienceModel> experienceList;
  final ExperienceModel? editingExperience;
  final bool isAddingNew;
  final bool isLoading;
  final String? errorMessage;
  final bool hasChanges;
  final bool? isSuccess;

  ExperienceState({
    this.experienceList = const [],
    this.editingExperience,
    this.isAddingNew = false,
    this.isLoading = false,
    this.errorMessage,
    this.hasChanges = false,
    this.isSuccess,
  });

  ExperienceState copyWith({
    List<ExperienceModel>? experienceList,
    ExperienceModel? editingExperience,
    bool? isAddingNew,
    bool? isLoading,
    String? errorMessage,
    bool? hasChanges,
    bool? isSuccess,
  }) {
    return ExperienceState(
      experienceList: experienceList ?? this.experienceList,
      editingExperience: editingExperience ?? this.editingExperience,
      isAddingNew: isAddingNew ?? this.isAddingNew,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      hasChanges: hasChanges ?? this.hasChanges,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
}
