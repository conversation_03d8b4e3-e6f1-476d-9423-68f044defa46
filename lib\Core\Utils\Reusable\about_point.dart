import 'package:devfolio/Core/Utils/Reusable/custom_card.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_icon.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';

class AboutPoint extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;

  const AboutPoint({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Row(
        children: [
          CustomIcon(
            icon: icon,
            size: ResponsiveLayout.getLargeIconSize(context),
            color: AppColors.primary,
          ),
          SizedBox(width: ResponsiveLayout.getMediumSpacing(context)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: title,
                  fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                  fontWeight: FontWeight.bold,
                ),
                SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
                CustomText(
                  text: description,
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
