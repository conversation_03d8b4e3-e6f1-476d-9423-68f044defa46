import '../../../../../Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/skill_model.dart';
import '../../../../../Core/resources/resources.dart';
import 'skill_card.dart';

class SkillsList extends StatelessWidget {
  final List<SkillModel> skills;
  final SkillModel? selectedSkill;
  final Function(SkillModel) onDelete;
  final Function(SkillModel) onSelect;

  const SkillsList({
    super.key,
    required this.skills,
    required this.selectedSkill,
    required this.onDelete,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(15.w),
            decoration: BoxDecoration(
              color: AppColors.withOpacity(AppColors.primary, 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.list, color: AppColors.primary, size: 20.sp),
                SizedBox(width: 8.w),
                CustomText(
                  text: '${AppStrings.skills} (${skills.length})',
                  fontSize: ResponsiveLayout.getBodyFontSize(context),
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(15.w),
              itemCount: skills.length,
              itemBuilder: (context, index) {
                final skill = skills[index];
                return SkillCard(
                  skill: skill,
                  isSelected: selectedSkill?.id == skill.id,
                  onDelete: () => onDelete(skill),
                  onTap: () => onSelect(skill),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
