import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../Cubit/experience_cubit.dart';
import '../Cubit/experience_state.dart';

class ExperienceEditor extends StatefulWidget {
  const ExperienceEditor({super.key});

  @override
  State<ExperienceEditor> createState() => _ExperienceEditorState();
}

class _ExperienceEditorState extends State<ExperienceEditor> {
  @override
  void initState() {
    super.initState();
    context.read<ExperienceCubit>().loadExperience();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: ResponsiveLayout.getScreenPadding(context),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: 'Experience Management',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ExperienceCubit>().startAddingNew();
                    },
                    child: const Text('Add Experience'),
                  ),
                ],
              ),
              SizedBox(height: 20.h),

              // Content
              Expanded(
                child: BlocConsumer<ExperienceCubit, ExperienceState>(
                  listener: (context, state) {
                    if (state.errorMessage != null &&
                        state.errorMessage!.isNotEmpty) {
                      if (state.isSuccess == false) {
                        MessageWidget.show(
                          context,
                          type: MessageType.error,
                          message: state.errorMessage!,
                        );
                      }
                      if (state.isSuccess == true) {
                        MessageWidget.show(
                          context,
                          type: MessageType.success,
                          message: "Operation completed successfully",
                        );
                      }
                      context.read<ExperienceCubit>().resetSuccess();
                    }
                  },
                  builder: (context, state) {
                    if (state.isLoading && state.experienceList.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    }

                    // Show form if editing or adding
                    if (state.editingExperience?.id != null || state.isAddingNew) {
                      return _buildExperienceForm(context, state);
                    }

                    // Show list of experience entries
                    return _buildExperienceList(context, state);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExperienceForm(BuildContext context, ExperienceState state) {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        children: [
          // Form header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text: state.isAddingNew ? 'Add Experience' : 'Edit Experience',
                fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              IconButton(
                onPressed: () {
                  context.read<ExperienceCubit>().cancelEditing();
                },
                icon: Icon(Icons.close, color: Colors.grey, size: 24.sp),
              ),
            ],
          ),
          SizedBox(height: 20.h),

          // Form content
          Expanded(child: _buildExperienceFormFields(context, state)),
        ],
      ),
    );
  }

  Widget _buildExperienceFormFields(
    BuildContext context,
    ExperienceState state,
  ) {
    final companyController = TextEditingController(
      text: state.editingExperience?.company ?? '',
    );
    final positionController = TextEditingController(
      text: state.editingExperience?.position ?? '',
    );
    final startDateController = TextEditingController(
      text: state.editingExperience?.startDate ?? '',
    );
    final endDateController = TextEditingController(
      text: state.editingExperience?.endDate ?? '',
    );
    final descriptionController = TextEditingController(
      text: state.editingExperience?.description ?? '',
    );
    final locationController = TextEditingController(
      text: state.editingExperience?.location ?? '',
    );
    final technologiesController = TextEditingController(
      text: state.editingExperience?.technologies?.join(', ') ?? '',
    );

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTextField(companyController, 'Company', Icons.business),
          SizedBox(height: 15.h),
          _buildTextField(positionController, 'Position/Job Title', Icons.work),
          SizedBox(height: 15.h),
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  startDateController,
                  'Start Date',
                  Icons.calendar_today,
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: _buildTextField(
                  endDateController,
                  'End Date',
                  Icons.calendar_today,
                ),
              ),
            ],
          ),
          SizedBox(height: 15.h),
          _buildTextField(locationController, 'Location', Icons.location_on),
          SizedBox(height: 15.h),
          _buildTextField(
            technologiesController,
            'Technologies (comma separated)',
            Icons.code,
          ),
          SizedBox(height: 15.h),
          _buildTextField(
            descriptionController,
            'Description',
            Icons.description,
            maxLines: 4,
          ),
          SizedBox(height: 30.h),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    context.read<ExperienceCubit>().cancelEditing();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.withAlpha(50),
                    foregroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: state.isLoading
                      ? null
                      : () {
                          final technologies = technologiesController.text
                              .split(',')
                              .map((e) => e.trim())
                              .where((e) => e.isNotEmpty)
                              .toList();

                          context.read<ExperienceCubit>().saveExperience(
                            company: companyController.text,
                            position: positionController.text,
                            startDate: startDateController.text,
                            endDate: endDateController.text,
                            description: descriptionController.text,
                            location: locationController.text,
                            technologies: technologies,
                          );
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                  child: state.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Save'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon, {
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 20.sp),
            SizedBox(width: 8.w),
            CustomText(
              text: label,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          style: TextStyle(
            color: Colors.white,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 15.w,
              vertical: 15.h,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExperienceList(BuildContext context, ExperienceState state) {
    if (state.experienceList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.work, size: 64.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            CustomText(
              text: 'No experience entries yet',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              color: Colors.grey,
            ),
            SizedBox(height: 8.h),
            CustomText(
              text: 'Click "Add Experience" to get started',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(20.w),
      itemCount: state.experienceList.length,
      itemBuilder: (context, index) {
        final experience = state.experienceList[index];
        return Container(
          margin: EdgeInsets.only(bottom: 15.h),
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.grey.withAlpha(30), width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with position and actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: CustomText(
                      text: experience.position ?? 'No Position',
                      fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          context.read<ExperienceCubit>().startEditing(
                            experience,
                          );
                        },
                        icon: Icon(
                          Icons.edit,
                          color: AppColors.primary,
                          size: 20.sp,
                        ),
                      ),
                      IconButton(
                        onPressed: () async{
                       final result =  await _showDeleteConfirmation(context, experience);
                       if (result == true) {
                        if (context.mounted) {
                          context.read<ExperienceCubit>().deleteExperience(experience);
                        }
                       }
                        },
                        icon: Icon(
                          Icons.delete,
                          color: Colors.red,
                          size: 20.sp,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10.h),

              // Company and location
              Row(
                children: [
                  Icon(Icons.business, color: AppColors.primary, size: 16.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: CustomText(
                      text: experience.company ?? 'No Company',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              if (experience.location != null &&
                  experience.location!.isNotEmpty) ...[
                SizedBox(height: 5.h),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: AppColors.primary,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    CustomText(
                      text: experience.location!,
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.grey,
                    ),
                  ],
                ),
              ],

              // Dates
              SizedBox(height: 10.h),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: AppColors.primary,
                    size: 16.sp,
                  ),
                  SizedBox(width: 8.w),
                  CustomText(
                    text:
                        '${experience.startDate ?? 'N/A'} - ${experience.endDate ?? 'N/A'}',
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    color: Colors.grey,
                  ),
                ],
              ),

              // Technologies if available
              if (experience.technologies != null &&
                  experience.technologies!.isNotEmpty) ...[
                SizedBox(height: 10.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.code, color: AppColors.primary, size: 16.sp),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Wrap(
                        spacing: 8.w,
                        runSpacing: 4.h,
                        children: experience.technologies!
                            .map(
                              (tech) => Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(30),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: CustomText(
                                  text: tech,
                                  fontSize: ResponsiveLayout.getSmallFontSize(
                                    context,
                                  ),
                                  color: AppColors.primary,
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ],

              // Description if available
              if (experience.description != null &&
                  experience.description!.isNotEmpty) ...[
                SizedBox(height: 10.h),
                CustomText(
                  text: experience.description!,
                  fontSize: ResponsiveLayout.getBodyFontSize(context),
                  color: Colors.grey,
                  maxLines: 3,
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Future<bool> _showDeleteConfirmation(BuildContext context, experience)async {
 return await   showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: CustomText(
          text: 'Delete Experience',
          fontSize: ResponsiveLayout.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        content: CustomText(
          text:
              'Are you sure you want to delete this experience entry? This action cannot be undone.',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
          color: Colors.grey,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: CustomText(
              text: 'Cancel',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
