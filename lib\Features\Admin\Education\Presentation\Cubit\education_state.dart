import '../../../../../Core/models/education_model.dart';

class EducationState {
  final List<EducationModel> educationList;
  final EducationModel? editingEducation;
  final bool isAddingNew;
  final bool isLoading;
  final String? errorMessage;
  final bool hasChanges;
  final bool? isSuccess;

  EducationState({
    this.educationList = const [],
    this.editingEducation,
    this.isAddingNew = false,
    this.isLoading = false,
    this.errorMessage,
    this.hasChanges = false,
    this.isSuccess,
  });

  EducationState copyWith({
    List<EducationModel>? educationList,
    EducationModel? editingEducation,
    bool? isAddingNew,
    bool? isLoading,
    String? errorMessage,
    bool? hasChanges,
    bool? isSuccess,
  }) {
    return EducationState(
      educationList: educationList ?? this.educationList,
      editingEducation: editingEducation ?? this.editingEducation,
      isAddingNew: isAddingNew ?? this.isAddingNew,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      hasChanges: hasChanges ?? this.hasChanges,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EducationState &&
        other.educationList == educationList &&
        other.editingEducation == editingEducation &&
        other.isAddingNew == isAddingNew &&
        other.isLoading == isLoading &&
        other.errorMessage == errorMessage &&
        other.hasChanges == hasChanges &&
        other.isSuccess == isSuccess;
  }

  @override
  int get hashCode {
    return educationList.hashCode ^
        editingEducation.hashCode ^
        isAddingNew.hashCode ^
        isLoading.hashCode ^
        errorMessage.hashCode ^
        hasChanges.hashCode ^
        isSuccess.hashCode;
  }

  @override
  String toString() {
    return 'EducationState(educationList: $educationList, editingEducation: $editingEducation, isAddingNew: $isAddingNew, isLoading: $isLoading, errorMessage: $errorMessage, hasChanges: $hasChanges, isSuccess: $isSuccess)';
  }
}
