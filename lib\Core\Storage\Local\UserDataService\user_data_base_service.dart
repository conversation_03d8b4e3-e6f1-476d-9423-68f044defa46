import 'package:hive/hive.dart';
import '../local_storage_keys.dart';

mixin UserDataBaseService {
  static late Box _userBox;
  static const String _userBoxName = 'user_data_box';

  static Future<void> init() async {
    _userBox = await Hive.openBox(_userBoxName);
  }

  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    try {
      await _userBox.put(LocalStorageKeys.userDataId, userData["userId"]);
      await _userBox.put(LocalStorageKeys.userData, userData);
    } catch (e) {
      rethrow; // Re-throw the error for external handling
    }
  }

  static Map<String, dynamic>? getUserData() {
    try {
      final userData = _userBox.get(LocalStorageKeys.userData);
      if (userData != null) {
        return Map<String, dynamic>.from(userData as Map);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  
  static String getUserDataId() => _userBox.get(LocalStorageKeys.userDataId , defaultValue: '');
  static Future<void> updateUserData(Map<String, dynamic> updates) async {
    try {
      final currentData = getUserData() ?? {};
      final updatedData = {...currentData, ...updates};

      await _userBox.put(LocalStorageKeys.userData, updatedData);
    } catch (e) {
      rethrow; // Re-throw the error for external handling
    }
  }

  static bool hasUserData() => getUserData() != null;
  
  static String getEmailUser() {
    final userData = getUserData();
    var email = userData?["emailUser"] ?? '';
    if (email != null) {
      return email.split('@').first ;
    }
    return '';
  }
  static Future<void> clearUserData() async {
    try {
      await _userBox.delete(LocalStorageKeys.userData);
    } catch (e) {
      rethrow; // Re-throw the error for external handling
    }
  }

  static Future<void> close() async {
    await _userBox.close();
  }
}
