import 'package:devfolio/Core/Utils/Reusable/experience_card.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:flutter/material.dart';
import '../../Core/layout/responsive_layout.dart';

class ExperienceScreen extends StatelessWidget {
  const ExperienceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'Experience',
      child: SingleChildScrollView(
        child: Column(
          children: [
            ExperienceCard(
              company: 'Tech Solutions Inc.',
              position: 'Senior Flutter Developer',
              duration: '2022 - Present',
              description:
                  'Leading mobile app development team and mentoring junior developers. Implemented complex features and optimized app performance.',
              achievements: [
                'Led development of 5+ mobile applications',
                'Improved app performance by 40%',
                'Mentored 3 junior developers',
                'Implemented CI/CD pipeline',
              ],
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            ExperienceCard(
              company: 'Mobile Apps Co.',
              position: 'Flutter Developer',
              duration: '2020 - 2022',
              description:
                  'Developed cross-platform mobile applications using Flutter and Dart. Collaborated with design and backend teams.',
              achievements: [
                'Developed 10+ mobile applications',
                'Integrated REST APIs and Firebase',
                'Implemented state management with BLoC',
                'Created reusable widget libraries',
              ],
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            ExperienceCard(
              company: 'Startup Ventures',
              position: 'Junior Developer',
              duration: '2019 - 2020',
              description:
                  'Started career in mobile development, learning Flutter and contributing to various projects.',
              achievements: [
                'Learned Flutter and Dart fundamentals',
                'Contributed to 3 mobile projects',
                'Participated in code reviews',
                'Attended Flutter conferences',
              ],
            ),
          ],
        ),
      ),
    );
  }
}
