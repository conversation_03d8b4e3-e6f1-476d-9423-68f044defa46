class ExperienceModel {
  final String? id;
  final String? company;
  final String? position;
  final String? startDate;
  final String? endDate;
  final String? description;
  final List<String>? technologies;
  final String? location;

  ExperienceModel({
    this.id,
    this.company,
    this.position,
    this.startDate,
    this.endDate,
    this.description,
    this.technologies,
    this.location,
  });

  factory ExperienceModel.fromJson(Map<String, dynamic> json) {
    return ExperienceModel(
      id: json['id'],
      company: json['company'] ?? json['companyName'],
      position: json['position'] ?? json['jobTitle'],
      startDate: json['startDate'] ?? json['start_date'],
      endDate: json['endDate'] ?? json['end_date'],
      description: json['description'],
      technologies: (json['technologies'] as List?)
          ?.map((e) => e.toString())
          .toList(),
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (company != null) 'company': company,
      if (position != null) 'position': position,
      if (startDate != null) 'startDate': startDate,
      if (endDate != null) 'endDate': endDate,
      if (description != null) 'description': description,
      if (technologies != null) 'technologies': technologies,
      if (location != null) 'location': location,
    };
  }

  ExperienceModel copyWith({
    String? id,
    String? company,
    String? position,
    String? startDate,
    String? endDate,
    String? description,
    List<String>? technologies,
    String? location,
  }) {
    return ExperienceModel(
      id: id ?? this.id,
      company: company ?? this.company,
      position: position ?? this.position,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      description: description ?? this.description,
      technologies: technologies ?? this.technologies,
      location: location ?? this.location,
    );
  }

  static String generateTimeIdString() {
    return (DateTime.now().millisecondsSinceEpoch % 1000000).toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExperienceModel &&
        other.id == id &&
        other.company == company &&
        other.position == position &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.description == description &&
        other.technologies == technologies &&
        other.location == location;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        company.hashCode ^
        position.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        description.hashCode ^
        technologies.hashCode ^
        location.hashCode;
  }

  @override
  String toString() {
    return 'ExperienceModel(id: $id, company: $company, position: $position, startDate: $startDate, endDate: $endDate, description: $description, technologies: $technologies, location: $location)';
  }
}
