import 'package:devfolio/config/Routes/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/Utils/Reusable/custom_button.dart';
import '../../../../../Core/Utils/Reusable/custom_text.dart';
import '../../../../../Core/Utils/widgets/build_text_field.dart';
import '../../../../../Core/Utils/widgets/password_text__form_field.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../main.dart';
import '../../../Data/Cubit/auth_cubit.dart';
import '../../../Data/Models/auth_model.dart';
import '../auth_header.dart';

class BuildAuthCard extends StatelessWidget {
  const BuildAuthCard({
    super.key,
    required this.context,
    required GlobalKey<FormState> formKey,
    required TextEditingController emailController,
    required TextEditingController passwordController,
  }) : _formKey = formKey,
       _emailController = emailController,
       _passwordController = passwordController;

  final BuildContext context;
  final GlobalKey<FormState> _formKey;
  final TextEditingController _emailController;
  final TextEditingController _passwordController;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ResponsiveLayout.isMobile(context) ? double.infinity : 400.w,
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AuthHeader(
            title: 'Welcome Back',
            subtitle: 'Sign in to access your dashboard',
            icon: Icons.login,
          ),
          SizedBox(height: 32.h),
          Form(
            key: _formKey,
            child: Column(
              children: [
                BuildTextField(
                  controller: _emailController,
                  label: 'Email',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  hint: 'Enter your email',
                ),
                SizedBox(height: 20.h),
                CustomPasswordTextFromField(
                  controller: _passwordController,
                  fieldId: 'password',
                  hintText: 'Enter your password',
                ),
                SizedBox(height: 24.h),
                CustomButton(
                  text: 'Sign In',
                  onPressed: _handleLogin,
                  isLoading:
                      context.watch<AuthCubit>().state.isLoading ?? false,
                ),
                SizedBox(height: 20.h),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomText(
                      text: "Don't have an account? ",
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: AppColors.textSecondary,
                    ),
                    GestureDetector(
                      onTap: () {
                        context.read<AuthCubit>().clearState();
                        kNavigationService.navigateToNamed(RouteNames.registerName);
                      },
                      child: CustomText(
                        text: 'Sign Up',
                        fontSize: ResponsiveLayout.getBodyFontSize(context),
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogin() {
    if (_formKey.currentState!.validate()) {
      final authModel = AuthModel(
        name: '',
        phone: '',
        email: _emailController.text,
        password: _passwordController.text,
      );
      context.read<AuthCubit>().login(authModel);
    }
  }
}
