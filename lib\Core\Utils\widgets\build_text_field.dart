import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildTextField extends StatelessWidget {
  const BuildTextField({
    super.key,
    required this.controller,
    required this.label,
    required this.icon,
    this.keyboardType,
    this.maxLines = 1,
    this.hint, 
    this.suffixIcon,
    this.validator,
    this.isPassword = false,
  });

  final TextEditingController controller;
  final String label;
  final IconData icon;
  final TextInputType? keyboardType;
  final int maxLines;
  final String? hint;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final bool isPassword;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 20.sp),
            SizedBox(width: 8.w),
            CustomText(
              text: label,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              fontWeight: FontWeight.w600,
            ),
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          textInputAction: TextInputAction.next,
          obscureText: isPassword,
          maxLines: maxLines,
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
          ),
          decoration: InputDecoration(
            suffixIcon: suffixIcon,
            hintText: hint,
            hintStyle: TextStyle(
              color: AppColors.withOpacity(AppColors.textSecondary, 0.6),
              fontSize: ResponsiveLayout.getBodyFontSize(context),
            ),
            filled: true,
            fillColor: AppColors.cardSurface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(color: AppColors.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(color: AppColors.error, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
          ),
          
          validator: validator,
        ),
      ],
    );
  }
}
