import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/skill_model.dart';
import '../../../../../Core/resources/app_strings.dart';
import '../../Data/skills_source.dart';
import 'skills_state.dart';

class SkillsCubit extends Cubit<SkillsState> {
  SkillsCubit() : super(SkillsState());
  String id = '';
  // Load skills data
  Future<void> initSkills(List<SkillModel> skills, String id) async {
    this.id = id;
    emit(
      state.copyWith(
        isLoading: false,
        errorMessage: null,
        isAddingNew: true,
        isEditing: false,
        editingSkill: SkillModel(id: '', name: '', category: '', icon: ''),
        onIconSelected: "",
        skillsList: skills,
      ),
    );
  }

  Future<void> addNewSkill(SkillModel skill) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final skillsName = state.skillsList
        .map((e) => e.name?.toLowerCase())
        .toList();
    if (skillsName.contains(skill.name?.toLowerCase())) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: "Skill already exists",
          isSuccess: false,
        ),
      );
      return;
    }
    final result = await SkillsSource.addSkill(skill, id);
    result.fold(
      (l) => emit(
        state.copyWith(isLoading: false, errorMessage: l, isSuccess: false),
      ),
      (r) => emit(
        state.copyWith(
          isSuccess: true,
          isLoading: false,
          skillsList: [...state.skillsList, skill],
        ),
      ),
    );
  }

  Future<void> editSkill(SkillModel skill) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final result = await SkillsSource.updateSkill(skill, id);
    result.fold(
      (l) => emit(
        state.copyWith(isLoading: false, errorMessage: l, isSuccess: false),
      ),
      (r) {
        final index = state.skillsList.indexWhere(
          (element) => element.id == skill.id,
        );
        if (index != -1) {
          final updatedSkills = List<SkillModel>.from(state.skillsList);
          updatedSkills[index] = skill;
          emit(
            state.copyWith(
              isLoading: false,
              skillsList: updatedSkills,
              isSuccess: true,
              isAddingNew: true,
              isEditing: false,
              editingSkill: SkillModel(
                id: '',
                name: '',
                category: '',
                icon: '',
              ),
            ),
          );
        }
      },
    );
  }

  Future<void> deleteSkill(SkillModel skill) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final result = await SkillsSource.deleteSkill(skill.id!, id);
    result.fold(
      (l) => emit(
        state.copyWith(isLoading: false, errorMessage: l, isSuccess: false),
      ),
      (r) {
        final index = state.skillsList.indexWhere(
          (element) => element.id == skill.id,
        );
        if (index != -1) {
          final updatedSkills = List<SkillModel>.from(state.skillsList);
          updatedSkills.removeAt(index);
          emit(state.copyWith(isLoading: false, skillsList: updatedSkills));
        }
      },
    );
  }

  void resetSuccess() {
    emit(state.copyWith(isSuccess: null));
  }

  void newOrEdit({bool? newSkill, bool? editingSkill}) {
    if (newSkill == true && editingSkill == true) return;
    if (newSkill == false && editingSkill == false) return;
    emit(
      state.copyWith(
        isAddingNew: newSkill ?? state.isAddingNew,
        isEditing: editingSkill ?? state.isEditing,
      ),
    );
  }

  void selectSkill(SkillModel? skill) {
    emit(state.copyWith(editingSkill: skill, onIconSelected: ""));
  }

  void onIconSelected(String icon) {
    if (state.isAddingNew == true) {
      emit(state.copyWith(onIconSelected: icon));
    } else {
      emit(
        state.copyWith(
          onIconSelected: "",
          editingSkill: state.editingSkill?.copyWith(icon: icon),
        ),
      );
    }
  }

  String getIcon() {
    final iconSelected = state.onIconSelected;
    final iconEditing = state.editingSkill?.icon;
    var icon = "";
    if (state.isAddingNew == true) {
       icon =  iconSelected ?? AppStrings.codeIcon;
    } else {
      icon = iconEditing ?? AppStrings.codeIcon;
    }
    return icon;
  }
}
