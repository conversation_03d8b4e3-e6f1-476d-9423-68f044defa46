import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AdminSaveButton extends StatelessWidget {
  final bool hasChanges;
  final VoidCallback? onPressed;

  const AdminSaveButton({super.key, required this.hasChanges, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: SizedBox(
        width: double.infinity,
        height: 50.h,
        child: ElevatedButton(
          onPressed: hasChanges ? onPressed : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: hasChanges
                ? AppColors.primary
                : AppColors.success.withAlpha(20),
            foregroundColor: hasChanges
                ? AppColors.textPrimary
                : AppColors.success,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
            ),
          ),
          child: CustomText(
            text: hasChanges ? AppStrings.saveChanges : AppStrings.allSaved,
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
