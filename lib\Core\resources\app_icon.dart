import 'package:flutter/material.dart';

class AppIcon {
static  IconData getIconData(String iconName) {
    switch (iconName) {
      case 'flutter':
        return Icons.flutter_dash;
      case 'code':
        return Icons.code;
      case 'cloud':
        return Icons.cloud;
      case 'git':
        return Icons.code;
      case 'api':
        return Icons.api;
      case 'design':
        return Icons.design_services;
      case 'database':
        return Icons.storage;
      case 'test':
        return Icons.bug_report;
      case 'deploy':
        return Icons.rocket_launch;
      case 'mobile':
        return Icons.phone_android;
      case 'web':
        return Icons.web;
      case 'server':
        return Icons.dns;
      case 'security':
        return Icons.security;
      case 'analytics':
        return Icons.analytics;
      case 'ai':
        return Icons.psychology;
      default:
        return Icons.code;
    }
  }
  
}