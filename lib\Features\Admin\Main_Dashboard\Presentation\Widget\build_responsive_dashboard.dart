import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../config/cubit/admin_cubit.dart';
import 'build_recent_activity.dart';
import 'build_responsive_stats.dart';
import 'build_welcome_card.dart';

class BuildResponsiveDashboard extends StatefulWidget {
  const BuildResponsiveDashboard({
    super.key,
  });

  @override
  State<BuildResponsiveDashboard> createState() => _BuildResponsiveDashboardState();
}

class _BuildResponsiveDashboardState extends State<BuildResponsiveDashboard> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AdminCubit>().init();
    });
  }
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        spacing: 20.h,
        children: [
          BuildWelcomeCard(),
          BuildResponsiveStats(),
          BuildRecentActivity(),
        ],
      ),
    );
  }
}
