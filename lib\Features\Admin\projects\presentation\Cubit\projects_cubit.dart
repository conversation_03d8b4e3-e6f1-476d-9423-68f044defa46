import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/project_model.dart';
import '../../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../Data/projects_source.dart';
import '../../Data/project_image_service.dart';
import 'projects_state.dart';

class ProjectsCubit extends Cubit<ProjectsState> {
  ProjectsCubit() : super(ProjectsState());

  // Load projects data
  Future<void> loadProjects({bool refresh = false}) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      final result = await ProjectsSource.getProjects(
        refresh: refresh,
        userId: userId.isNotEmpty ? userId : null,
      );

      result.fold(
        (error) => emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to load projects: $error',
            isSuccess: false,
          ),
        ),
        (projectsList) => emit(
          state.copyWith(
            projectsList: projectsList,
            isLoading: false,
            hasChanges: false,
            isSuccess: true,
            errorMessage: null,
          ),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to load projects data: $e',
          isSuccess: false,
        ),
      );
    }
  }

  // Add new project
  void addNewProject() {
    emit(
      state.copyWith(
        editingProject: null,
        isAddingNew: true,
        errorMessage: null,
      ),
    );
  }

  // Edit project
  void editProject(ProjectModel project) {
    emit(
      state.copyWith(
        editingProject: project,
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Cancel edit
  void cancelEdit() {
    log("cancelEdit");
    log("state.editingProject ::: ${state.editingProject?.id}");
    emit(
      state.copyWith(
        editingProject: ProjectModel(),
        isAddingNew: false,
        errorMessage: null,
      ),
    );
  }

  // Save project
  Future<void> saveProject({
    required String? title,
    required String? description,
    required String? image,
    required String? githubUrl,
    required String? liveUrl,
    required String? category,
    required List<String>? technologies,
  }) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
      final userId = UserDataBaseService.getUserDataId();
      if (userId.isEmpty) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'User not authenticated',
            isSuccess: false,
          ),
        );
        return;
      }

      final project = ProjectModel(
        id: state.editingProject?.id ?? ProjectModel.generateTimeIdString(),
        title: title,
        description: description,
        image: image,
        github: githubUrl,
        liveUrl: liveUrl,
        category: category,
        technologies: technologies,
      );

      final result = state.isAddingNew
          ? await ProjectsSource.addProject(project, userId)
          : await ProjectsSource.updateProject(project, userId);

      result.fold(
        (error) => emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to save project: $error',
            isSuccess: false,
          ),
        ),
        (success) async {
          await loadProjects(refresh: true);
          emit(
            state.copyWith(
              editingProject: ProjectModel(),
              isAddingNew: false,
              isLoading: false,
              hasChanges: true,
              isSuccess: true,
              errorMessage: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to save project: $e',
          isSuccess: false,
        ),
      );
    }
  }

  // Delete project
  Future<void> deleteProject(ProjectModel project) async {
    emit(state.copyWith(isLoading: true, errorMessage: null, isSuccess: null));

    try {
    

      // Delete associated image if exists
      if (project.image != null && project.image!.isNotEmpty) {
        await ProjectImageService.deleteImage(project.image!);
      }
    final userId = UserDataBaseService.getUserDataId();
      final result = await ProjectsSource.deleteProject(project.id!, userId);

      result.fold(
        (error) => emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'Failed to delete project: $error',
            isSuccess: false,
          ),
        ),
        (success) async {
          await loadProjects(refresh: true);
          emit(
            state.copyWith(
              editingProject: state.editingProject?.id == project.id
                  ? null
                  : state.editingProject,
              isAddingNew: state.editingProject?.id == project.id
                  ? false
                  : state.isAddingNew,
              isLoading: false,
              hasChanges: true,
              isSuccess: true,
              errorMessage: null,
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to delete project: $e',
          isSuccess: false,
        ),
      );
    }
  }

  // Image upload functionality
  Future<String?> uploadProjectImage(String projectId) async {
    emit(state.copyWith(isUploadingImage: true, errorMessage: null));

    try {
      final imageResult = await ProjectImageService.pickImage();

      return await imageResult.fold(
        (error) {
          emit(state.copyWith(isUploadingImage: false, errorMessage: error));
          return null;
        },
        (imageBytes) async {
          final uploadResult = await ProjectImageService.uploadImage(
            imageBytes: imageBytes,
            projectId: projectId,
          );

          return await uploadResult.fold(
            (error) {
              emit(
                state.copyWith(
                  isUploadingImage: false,
                  errorMessage: 'Failed to upload image: $error',
                ),
              );
              return null;
            },
            (imageUrl) {
              emit(state.copyWith(isUploadingImage: false, errorMessage: null));
              return imageUrl;
            },
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          isUploadingImage: false,
          errorMessage: 'Failed to upload image: $e',
        ),
      );
      return null;
    }
  }

  // Reset success state
  void resetSuccess() {
    emit(state.copyWith(isSuccess: null, errorMessage: null));
  }

  // Clear error
  void clearError() {
    emit(state.copyWith(errorMessage: null));
  }

  // Reset state
  void reset() {
    emit(ProjectsState());
  }
}
