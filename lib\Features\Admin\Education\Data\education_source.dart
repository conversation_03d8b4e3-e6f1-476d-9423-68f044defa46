import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/models/education_model.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import 'package:dartz/dartz.dart';

class EducationSource {
  static Future<Either<String, bool>> addEducation(
    EducationModel education,
    String userId,
  ) async {
    try {
      final educationMap = education.toJson();

      final response = await SubabaseServices.addMapToList(
        table: 'portfolio_data',
        fieldName: 'education',
        mapToAdd: educationMap,
        matchColumn: 'userId',
        matchValue: userId,
      );

      if (response.status) {
        await UserDataService.addItemToList(LocalStorageKeys.education, educationMap);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> updateEducation(
    EducationModel education,
    String userId,
  ) async {
    try {
      final educationMap = education.toJson();
      final currentEducation = UserDataService.getListData(LocalStorageKeys.education);
      final existingEducation = currentEducation.firstWhere(
        (e) => e['id'] == education.id,
        orElse: () => {},
      );
      if (existingEducation.isEmpty) {
        return Left('Education entry not found for update');
      }
      final response = await SubabaseServices.updateMapInList(
        table: 'portfolio_data',
        fieldName: 'education',
        matchColumn: 'userId',
        matchValue: userId,
        updateByKey: 'id',
        updateByValue: education.id ?? '',
        newMapData: educationMap,
      );

      if (response.status) {
        await UserDataService.updateItemInList(
          LocalStorageKeys.education,
          education.id!,
          educationMap,
        );
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> deleteEducation(
    String id,
    String userId,
  ) async {
    try {
      final response = await SubabaseServices.deleteMapFromList(
        table: 'portfolio_data',
        fieldName: 'education',
        matchColumn: 'userId',
        matchValue: userId,
        deleteByKey: 'id',
        deleteByValue: id,
      );
      if (response.status) {
        await UserDataService.deleteItemFromList(LocalStorageKeys.education, id);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, List<EducationModel>>> getEducation({
    bool? refresh,
    String? userId,
  }) async {
    try {
      if (refresh == false || refresh == null) {
        final data = UserDataService.getListData(LocalStorageKeys.education);
        if (data.isNotEmpty) {
          final education = data.map((educationData) => EducationModel.fromJson(educationData)).toList();
          return Right(education);
        }
      }
      
      if (userId != null) {
        final response = await SubabaseServices.get(
          table: 'portfolio_data',
          filter: {'userId': userId},
        );
        
        if (response.status && response.data != null && response.data.isNotEmpty) {
          final portfolioData = response.data[0];
          final educationList = portfolioData['education'] as List<dynamic>? ?? [];
          final education = educationList
              .map((educationData) => EducationModel.fromJson(educationData as Map<String, dynamic>))
              .toList();
          
          // Update local cache
          await UserDataService.updateListData(
            LocalStorageKeys.education,
            educationList.map((e) => e as Map<String, dynamic>).toList(),
          );
          
          return Right(education);
        }
      }
      
      return Right([]);
    } catch (e) {
      return Left(e.toString());
    }
  }
}
