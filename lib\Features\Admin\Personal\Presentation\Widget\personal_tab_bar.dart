import 'package:devfolio/Features/Admin/Personal/Presentation/cubit/personal_contact_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/resources/resources.dart';

class PersonalTabBar extends StatelessWidget {
  final PersonalContactState state;
  final Function(int) onTabChanged;

  const PersonalTabBar({
    super.key,
    required this.state,
    required this.onTabChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
      ),
      child: Row(
        children: [
          _buildTabButton(context, 0, AppStrings.personalInfo, Icons.person),
          _buildTabButton(context, 1, AppStrings.contact, Icons.contact_mail),
          _buildTabButton(
            context,
            2,
            AppStrings.socialMediaProfiles,
            Icons.share,
          ),
          _buildTabButton(
            context,
            3,
            AppStrings.additionalInformation,
            Icons.info,
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(
    BuildContext context,
    int index,
    String label,
    IconData icon,
  ) {
    final isSelected = state.currentTab == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => onTabChanged(index),
        child: AnimatedContainer(
          duration: AppConstants.mediumAnimation,
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : AppColors.transparent,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected
                    ? AppColors.textPrimary
                    : AppColors.textSecondary,
                size: 20.sp,
              ),
              SizedBox(height: 4.h),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: isSelected
                      ? AppColors.textPrimary
                      : AppColors.textSecondary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
