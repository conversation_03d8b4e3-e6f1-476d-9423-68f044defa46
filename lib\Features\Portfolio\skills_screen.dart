import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:devfolio/Core/Utils/Reusable/skill_category.dart';
import 'package:flutter/material.dart';
import '../../Core/layout/responsive_layout.dart';

class SkillsScreen extends StatelessWidget {
  const SkillsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'Skills',
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Programming Languages
            SkillCategory(
              title: 'Programming Languages',
              skills: ['Dart', 'JavaScript', 'Python', 'Java'],
              icon: Icons.code,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Frameworks & Libraries
            SkillCategory(
              title: 'Frameworks & Libraries',
              skills: ['Flutter', 'React', 'Node.js', 'Express.js'],
              icon: Icons.library_books,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Databases
            SkillCategory(
              title: 'Databases',
              skills: ['Firebase', 'MongoDB', 'SQLite', 'PostgreSQL'],
              icon: Icons.storage,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Tools & Platforms
            SkillCategory(
              title: 'Tools & Platforms',
              skills: ['Git', 'Docker', 'AWS', 'Heroku'],
              icon: Icons.build,
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Soft Skills
            SkillCategory(
              title: 'Soft Skills',
              skills: [
                'Team Leadership',
                'Problem Solving',
                'Communication',
                'Agile',
              ],
              icon: Icons.people,
            ),
          ],
        ),
      ),
    );
  }
}
