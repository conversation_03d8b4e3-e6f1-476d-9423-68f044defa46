class ProjectModel {
  final String? id;
  final String? title;
  final String? description;
  final String? image;
  final List<String>? technologies;
  final String? github;
  final String? liveUrl;
  final String? category;

  ProjectModel({
    this.id,
    this.title,
    this.description,
    this.image,
    this.technologies,
    this.github,
    this.liveUrl,
    this.category,
  });

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      image: json['image'],
      technologies: (json['technologies'] as List?)
          ?.map((e) => e.toString())
          .toList(),
      // Handle both local storage format (github) and Supabase format (github_url)
      github: json['github'] ?? json['github_url'],
      // Handle both local storage format (liveUrl) and Supabase format (live_url)
      liveUrl: json['liveUrl'] ?? json['live_url'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (image != null) 'image': image,
      if (technologies != null) 'technologies': technologies,
      if (github != null) 'github': github,
      if (liveUrl != null) 'liveUrl': liveUrl,
      if (category != null) 'category': category,
    };
  }

  ProjectModel copyWith({
    String? id,
    String? title,
    String? description,
    String? image,
    List<String>? technologies,
    String? github,
    String? liveUrl,
    String? category,
  }) {
    return ProjectModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      image: image ?? this.image,
      technologies: technologies ?? this.technologies,
      github: github ?? this.github,
      liveUrl: liveUrl ?? this.liveUrl,
      category: category ?? this.category,
    );
  }

  static String generateTimeIdString() {
    return (DateTime.now().millisecondsSinceEpoch % 1000000).toString();
  }
}
