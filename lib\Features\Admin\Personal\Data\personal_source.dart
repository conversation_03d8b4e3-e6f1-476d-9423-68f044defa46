
import 'package:devfolio/Core/models/personal_info_model.dart';

import '../../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/services/Subabase/class_tables.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import 'package:dartz/dartz.dart';

class PersonalSource {
  static Future<Either<String, PersonalInfoModel>> getPersonalInfo({
    bool? refresh,
  }) async {
    try {
      if (refresh == false || refresh == null) {
        final data = UserDataService.getMapField(LocalStorageKeys.personalInfo);
        if (data != null) {
          final personalInfo = PersonalInfoModel.fromJson(data);
          return Right(personalInfo);
        }
      }
      final id = UserDataBaseService.getUserDataId();
      final response = await SubabaseServices.getMapField(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.personalInfo,
        matchColumn: 'userId',
        matchValue: id,
      );
      if (response.status) {
        // If data is null or empty, return empty PersonalInfoModel
        if (response.data == null) {
          return Right(PersonalInfoModel());
        }
        final data = PersonalInfoModel.fromJson(response.data);
        return Right(data);
      } else {
        // If no data found, return empty PersonalInfoModel instead of error
        return Right(PersonalInfoModel());
      }
    } catch (e) {
      // Return empty model instead of error for better UX
      return Right(PersonalInfoModel());
    }
  }

  static Future<Either<String, PersonalInfoModel>> updatePersonalInfo(
    PersonalInfoModel personalInfo,
    String id,
  ) async {
    try {
      final response = await SubabaseServices.updateMapField(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.personalInfo,
        newMap: personalInfo.toJson(),
        matchColumn: 'userId',
        matchValue: id,
      );
      if (response.status) {
        // Extract personalInfo from the full row data
        final personalInfoData = response.data[ClassTables.personalInfo];
        final data = PersonalInfoModel.fromJson(personalInfoData ?? {});
        UserDataService.updateMapField(
          LocalStorageKeys.personalInfo,
          data.toJson(),
        );
        return Right(data);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, PersonalInfoModel>> savePersonalInfo(
    PersonalInfoModel personalInfo,
    String id,
  ) async {
    try {
      final response = await SubabaseServices.insertMapField(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.personalInfo,
        newMap: personalInfo.toJson(),
        matchColumn: 'userId',
        matchValue: id,
      );
      if (response.status) {
        final data = PersonalInfoModel.fromJson(response.data);
        UserDataService.updateMapField(
          LocalStorageKeys.personalInfo,
          data.toJson(),
        );
        return Right(data);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }
}
