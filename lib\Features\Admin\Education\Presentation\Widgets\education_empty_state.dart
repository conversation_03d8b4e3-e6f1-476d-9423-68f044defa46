import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class EducationEmptyState extends StatelessWidget {
  final VoidCallback onAddPressed;

  const EducationEmptyState({
    super.key,
    required this.onAddPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Icon(
            Icons.school_outlined,
            size: 80.sp,
            color: Colors.grey.withAlpha(150),
          ),
          SizedBox(height: 20.h),
          
          // Title
          CustomText(
            text: 'No Education Entries',
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
          SizedBox(height: 10.h),
          
          // Description
          CustomText(
            text: 'Add your educational background to showcase your academic achievements',
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            color: Colors.grey.withAlpha(150),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          
          // Add button
          CustomButton(
            text: 'Add Your First Education',
            onPressed: onAddPressed,
            backgroundColor: AppColors.primary,
            textColor: Colors.white,
           
          ),
        ],
      ),
    );
  }
}
