import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/project_model.dart';
import '../Cubit/projects_cubit.dart';

class ProjectForm extends StatefulWidget {
  final ProjectModel? project;
  final Function(ProjectModel) onSave;
  final VoidCallback onCancel;

  const ProjectForm({
    super.key,
    this.project,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<ProjectForm> createState() => _ProjectFormState();
}

class _ProjectFormState extends State<ProjectForm> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _imageController;
  late TextEditingController _githubUrlController;
  late TextEditingController _liveUrlController;
  late TextEditingController _categoryController;
  late TextEditingController _technologiesController;

  String? _selectedImageUrl;
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.project?.title ?? '');
    _descriptionController = TextEditingController(
      text: widget.project?.description ?? '',
    );
    _imageController = TextEditingController(text: widget.project?.image ?? '');
    _githubUrlController = TextEditingController(
      text: widget.project?.github ?? '',
    );
    _liveUrlController = TextEditingController(
      text: widget.project?.liveUrl ?? '',
    );
    _categoryController = TextEditingController(
      text: widget.project?.category ?? '',
    );
    _technologiesController = TextEditingController(
      text: widget.project?.technologies?.join(', ') ?? '',
    );
    _selectedImageUrl = widget.project?.image;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _imageController.dispose();
    _githubUrlController.dispose();
    _liveUrlController.dispose();
    _categoryController.dispose();
    _technologiesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFF1A1A1A),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.r)),
      title: Row(
        children: [
          Icon(
            widget.project == null ? Icons.add : Icons.edit,
            color: const Color(0xFF6366F1),
            size: 24.sp,
          ),
          SizedBox(width: 10.w),
          CustomText(
            text: widget.project == null ? 'Add Project' : 'Edit Project',
            fontSize: ResponsiveLayout.getSubtitleFontSize(context),
            fontWeight: FontWeight.bold,
          ),
        ],
      ),
      content: SizedBox(
        width: 500.w,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTextField(_titleController, 'Project Title'),
                SizedBox(height: 15.h),
                _buildTextField(
                  _descriptionController,
                  'Description',
                  maxLines: 3,
                ),
                SizedBox(height: 15.h),
                _buildImageUploadSection(),
                SizedBox(height: 15.h),
                _buildTextField(_githubUrlController, 'GitHub URL'),
                SizedBox(height: 15.h),
                _buildTextField(_liveUrlController, 'Live URL'),
                SizedBox(height: 15.h),
                _buildTextField(_categoryController, 'Category'),
                SizedBox(height: 15.h),
                _buildTextField(
                  _technologiesController,
                  'Technologies (comma separated)',
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: widget.onCancel,
          child: const Text('Cancel', style: TextStyle(color: Colors.grey)),
        ),
        CustomButton(
          text: 'Save',
          onPressed: _saveProject,
          backgroundColor: const Color(0xFF6366F1),
        ),
      ],
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label, {
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: label,
          fontSize: ResponsiveLayout.getSmallFontSize(context),
          fontWeight: FontWeight.w600,
          color: Colors.grey,
        ),
        SizedBox(height: 5.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF2A2A2A),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: Color(0xFF6366F1)),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 8.h,
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '$label is required';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Project Image',
          fontSize: ResponsiveLayout.getSmallFontSize(context),
          fontWeight: FontWeight.w600,
          color: Colors.grey,
        ),
        SizedBox(height: 10.h),
        Container(
          width: double.infinity,
          height: 200.h,
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.withAlpha(30)),
          ),
          child: _selectedImageUrl != null && _selectedImageUrl!.isNotEmpty
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: Image.network(
                        _selectedImageUrl!,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildUploadPlaceholder();
                        },
                      ),
                    ),
                    Positioned(
                      top: 8.h,
                      right: 8.w,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(150),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () {
                            setState(() {
                              _selectedImageUrl = null;
                              _imageController.clear();
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                )
              : _buildUploadPlaceholder(),
        ),
        if (_isUploadingImage) ...[
          SizedBox(height: 10.h),
          const LinearProgressIndicator(
            color: Color(0xFF6366F1),
            backgroundColor: Color(0xFF2A2A2A),
          ),
        ],
      ],
    );
  }

  Widget _buildUploadPlaceholder() {
    return InkWell(
      onTap: _isUploadingImage ? null : _uploadImage,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.r)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.cloud_upload_outlined, size: 48.sp, color: Colors.grey),
            SizedBox(height: 10.h),
            CustomText(
              text: _isUploadingImage
                  ? 'Uploading...'
                  : 'Click to upload image',
              fontSize: ResponsiveLayout.getSmallFontSize(context),
              color: Colors.grey,
            ),
            SizedBox(height: 5.h),
            CustomText(
              text: 'Supports: JPG, PNG, GIF, WEBP (Max 5MB)',
              fontSize: ResponsiveLayout.getSmallFontSize(context) - 2,
              color: Colors.grey.withAlpha(150),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _uploadImage() async {
    setState(() {
      _isUploadingImage = true;
    });

    try {
      final projectId =
          widget.project?.id ?? ProjectModel.generateTimeIdString();
      final imageUrl = await context.read<ProjectsCubit>().uploadProjectImage(
        projectId,
      );

      if (imageUrl != null) {
        setState(() {
          _selectedImageUrl = imageUrl;
          _imageController.text = imageUrl;
          _isUploadingImage = false;
        });
      } else {
        setState(() {
          _isUploadingImage = false;
        });
      }
    } catch (e) {
      setState(() {
        _isUploadingImage = false;
      });
    }
  }

  void _saveProject() {
    if (_formKey.currentState!.validate()) {
      final project = ProjectModel(
        id: widget.project?.id ?? ProjectModel.generateTimeIdString(),
        title: _titleController.text,
        description: _descriptionController.text,
        image: _selectedImageUrl ?? _imageController.text,
        github: _githubUrlController.text,
        liveUrl: _liveUrlController.text,
        category: _categoryController.text,
        technologies: _technologiesController.text
            .split(',')
            .map((e) => e.trim())
            .toList(),
      );
      widget.onSave(project);
    }
  }
}
