import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/models/experience_model.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import 'package:dartz/dartz.dart';

class ExperienceSource {
  static Future<Either<String, bool>> addExperience(
    ExperienceModel experience,
    String userId,
  ) async {
    try {
      final experienceMap = experience.toJson();

      final response = await SubabaseServices.addMapToList(
        table: 'portfolio_data',
        fieldName: 'experience',
        mapToAdd: experienceMap,
        matchColumn: 'userId',
        matchValue: userId,
      );

      if (response.status) {
        await UserDataService.addItemToList(LocalStorageKeys.experience, experienceMap);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> updateExperience(
    ExperienceModel experience,
    String userId,
  ) async {
    try {
      final experienceMap = experience.toJson();
      final currentExperience = UserDataService.getListData(LocalStorageKeys.experience);
      final existingExperience = currentExperience.firstWhere(
        (e) => e['id'] == experience.id,
        orElse: () => {},
      );
      if (existingExperience.isEmpty) {
        return Left('Experience entry not found for update');
      }
      final response = await SubabaseServices.updateMapInList(
        table: 'portfolio_data',
        fieldName: 'experience',
        matchColumn: 'userId',
        matchValue: userId,
        updateByKey: 'id',
        updateByValue: experience.id ?? '',
        newMapData: experienceMap,
      );

      if (response.status) {
        await UserDataService.updateItemInList(
          LocalStorageKeys.experience,
          experience.id!,
          experienceMap,
        );
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> deleteExperience(
    String id,
    String userId,
  ) async {
    try {
      final response = await SubabaseServices.deleteMapFromList(
        table: 'portfolio_data',
        fieldName: 'experience',
        matchColumn: 'userId',
        matchValue: userId,
        deleteByKey: 'id',
        deleteByValue: id,
      );
      if (response.status) {
        await UserDataService.deleteItemFromList(LocalStorageKeys.experience, id);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, List<ExperienceModel>>> getExperience({
    bool? refresh,
    String? userId,
  }) async {
    try {
      if (refresh == false || refresh == null) {
        final data = UserDataService.getListData(LocalStorageKeys.experience);
        if (data.isNotEmpty) {
          final experience = data.map((experienceData) => ExperienceModel.fromJson(experienceData)).toList();
          return Right(experience);
        }
      }
      
      if (userId != null) {
        final response = await SubabaseServices.get(
          table: 'portfolio_data',
          filter: {'userId': userId},
        );
        
        if (response.status && response.data != null && response.data.isNotEmpty) {
          final portfolioData = response.data[0];
          final experienceList = portfolioData['experience'] as List<dynamic>? ?? [];
          final experience = experienceList
              .map((experienceData) => ExperienceModel.fromJson(experienceData as Map<String, dynamic>))
              .toList();
          
          // Update local cache
          await UserDataService.updateListData(
            LocalStorageKeys.experience,
            experienceList.map((e) => e as Map<String, dynamic>).toList(),
          );
          
          return Right(experience);
        }
      }
      
      return Right([]);
    } catch (e) {
      return Left(e.toString());
    }
  }
}
