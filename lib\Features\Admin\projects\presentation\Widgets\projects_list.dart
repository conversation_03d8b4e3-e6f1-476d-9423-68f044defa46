import 'package:flutter/material.dart';
import '../../../../../Core/models/project_model.dart';
import 'project_card.dart';

class ProjectsList extends StatelessWidget {
  final List<ProjectModel> projects;
  final Function(ProjectModel) onEdit;
  final Function(ProjectModel) onDelete;

  const ProjectsList({
    super.key,
    required this.projects,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        return ProjectCard(
          project: project,
          onEdit: () => onEdit(project),
          onDelete: () => onDelete(project),
        );
      },
    );
  }
}
