import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/resources/resources.dart';

class PersonalActionButtons extends StatelessWidget {
  final bool isLoading;
  final VoidCallback onSave;
  final VoidCallback onReset;

  const PersonalActionButtons({
    super.key,
    required this.isLoading,
    required this.onSave,
    required this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    return  Row(
      children: [ 
        Expanded(
          child: CustomButton(
            text: isLoading ? AppStrings.saving : AppStrings.saveChanges,
            onPressed: isLoading ? null : onSave,
            backgroundColor: AppColors.primary,
          ),
        ),
        SizedBox(width: 15.w),
        Expanded(
          child: CustomButton(
            text: AppStrings.resetForm,
            onPressed: onReset,
            backgroundColor: AppColors.transparent,
            textColor: AppColors.primary,
          ),
        ),
      ],
    );
  }
}
