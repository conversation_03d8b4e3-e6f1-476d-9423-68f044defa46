import '../../../../../Core/models/personal_info_model.dart';

class PersonalContactState {
  final bool isLoading;
  final bool hasChanges;
  final String? errorMessage;
  final PersonalInfoModel? personalData;
  final int currentTab;
  final PersonalInfoModel? currentData;

  PersonalContactState({
    this.isLoading = false,
    this.hasChanges = false,
    this.errorMessage,
    this.personalData ,
    this.currentTab = 0,
    this.currentData,
  });

  PersonalContactState copyWith({
    bool? isLoading,
    bool? hasChanges,
    String? errorMessage,
    PersonalInfoModel? personalData,
    int? currentTab,
    PersonalInfoModel? currentData,
  }) {
    return PersonalContactState(
      isLoading: isLoading ?? this.isLoading,
      hasChanges: hasChanges ?? this.hasChanges,
      errorMessage: errorMessage,
      personalData: personalData ?? this.personalData,
      currentTab: currentTab ?? this.currentTab,
      currentData: currentData ?? this.currentData,
    );
  }
}
