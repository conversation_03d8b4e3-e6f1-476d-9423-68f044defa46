import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../Reusable/custom_text.dart';
import '../../resources/app_colors.dart';
import '../../layout/responsive_layout.dart';

enum MessageType { success, error, warning, info }

class MessageWidget {
  static void show(
    BuildContext context, {
    required MessageType type,
    required String message,
    Duration duration = const Duration(seconds: 5),
  }) {
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => _MessageOverlay(
        type: type,
        message: message,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    Overlay.of(context).insert(overlayEntry);

    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  // Convenience methods
  static void showSuccess(BuildContext context, String message) {
    show(context, type: MessageType.success, message: message);
  }

  static void showError(BuildContext context, String message) {
    show(context, type: MessageType.error, message: message);
  }

  static void showWarning(BuildContext context, String message) {
    show(context, type: MessageType.warning, message: message);
  }

  static void showInfo(BuildContext context, String message) {
    show(context, type: MessageType.info, message: message);
  }
}

class _MessageOverlay extends StatefulWidget {
  final MessageType type;
  final String message;
  final VoidCallback onDismiss;

  const _MessageOverlay({
    required this.type,
    required this.message,
    required this.onDismiss,
  });

  @override
  State<_MessageOverlay> createState() => _MessageOverlayState();
}

class _MessageOverlayState extends State<_MessageOverlay> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
          child: Align(
            alignment: Alignment.topCenter,
            child: _buildMessageContainer(),
          ),
        ),
      ),
    );
  }

  Widget _buildMessageContainer() {
    return Container(
          constraints: BoxConstraints(maxWidth: 400.w),
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
          decoration: _buildDecoration(),
          child: Row(
            children: [
              _buildIcon(),
              SizedBox(width: 12.w),
              _buildMessageText(),
              SizedBox(width: 8.w),
              _buildCloseButton(),
            ],
          ),
        )
        .animate()
        .slideY(begin: -1, duration: 300.ms, curve: Curves.easeOut)
        .fadeIn(duration: 300.ms);
  }

  BoxDecoration _buildDecoration() {
    return BoxDecoration(
      color: AppColors.cardBackground,
      borderRadius: BorderRadius.circular(12.r),
      border: Border.all(color: _getBorderColor(), width: 1),
      boxShadow: [
        BoxShadow(
          color: AppColors.shadowDark,
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  Widget _buildIcon() {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(),
        shape: BoxShape.circle,
      ),
      child: Icon(_getIcon(), color: _getIconColor(), size: 20.sp),
    );
  }

  Widget _buildMessageText() {
    return Expanded(
      child: CustomText(
        text: widget.message,
        fontSize: ResponsiveLayout.getBodyFontSize(context),
        color: AppColors.textPrimary,
        maxLines: 3,
      ),
    );
  }

  Widget _buildCloseButton() {
    return GestureDetector(
      onTap: widget.onDismiss,
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: AppColors.overlayLight,
          shape: BoxShape.circle,
        ),
        child: Icon(Icons.close, color: AppColors.textPrimary, size: 16.sp),
      ),
    );
  }

  Color _getBorderColor() {
    switch (widget.type) {
      case MessageType.success:
        return AppColors.success;
      case MessageType.error:
        return AppColors.error;
      case MessageType.warning:
        return AppColors.warning;
      case MessageType.info:
        return AppColors.primary;
    }
  }

  Color _getIconColor() {
    switch (widget.type) {
      case MessageType.success:
        return AppColors.success;
      case MessageType.error:
        return AppColors.error;
      case MessageType.warning:
        return AppColors.warning;
      case MessageType.info:
        return AppColors.primary;
    }
  }

  Color _getIconBackgroundColor() {
    switch (widget.type) {
      case MessageType.success:
        return AppColors.success.withValues(alpha: 0.1);
      case MessageType.error:
        return AppColors.error.withValues(alpha: 0.1);
      case MessageType.warning:
        return AppColors.warning.withValues(alpha: 0.1);
      case MessageType.info:
        return AppColors.primary.withValues(alpha: 0.1);
    }
  }

  IconData _getIcon() {
    switch (widget.type) {
      case MessageType.success:
        return Icons.check_circle;
      case MessageType.error:
        return Icons.error;
      case MessageType.warning:
        return Icons.warning;
      case MessageType.info:
        return Icons.info;
    }
  }
}
