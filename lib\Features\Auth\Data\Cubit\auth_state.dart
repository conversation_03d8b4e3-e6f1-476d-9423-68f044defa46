import '../../../../Core/models/portfolio_data_model.dart';

class AuthState {
  final String? message;
  final bool? isLoading;
  final PortfolioDataModel? portfolioData;
  final bool? isSuccess;

  AuthState({
    this.message,
    this.isLoading ,
    this.portfolioData,
    this.isSuccess ,
  });

  AuthState copyWith({
    String? message,
    bool? isLoading,
    PortfolioDataModel? portfolioData,
    bool? isSuccess,
  }) {
    return AuthState(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      portfolioData: portfolioData ?? this.portfolioData,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
}
