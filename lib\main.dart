import 'package:flutter/material.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'Core/Storage/Local/UserDataService/user_data_base_service.dart';
import 'Core/services/Subabase/subabase_services.dart';
import 'auth_app.dart';
import 'config/Routes/index.dart';
import 'config/subabase_keys.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  usePathUrlStrategy();

  await Future.wait([
    Hive.initFlutter(),
    UserDataBaseService.init(),
    Supabase.initialize(
      url: SupabaseKeys.url,
      anonKey: SupabaseKeys.apiKey,
    ).then((_) => SubabaseServices.init()),
  ]);

  runApp(const AuthApp());
  
}

final AppNavigationService kNavigationService = AppNavigationService();
