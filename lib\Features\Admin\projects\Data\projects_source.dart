import 'dart:developer';

import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/models/project_model.dart';
import '../../../../Core/services/Subabase/class_tables.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import '../../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import 'package:dartz/dartz.dart';

class ProjectsSource {
  static Future<Either<String, bool>> addProject(
    ProjectModel project,
    String userId,
  ) async {
    try {
      final projectMap = project.toJson();

      final response = await SubabaseServices.addMapToList(
        table: 'portfolio_data',
        fieldName: 'projects',
        mapToAdd: projectMap,
        matchColumn: 'userId',
        matchValue: userId,
      );

      if (response.status) {
        await UserDataService.addItemToList(LocalStorageKeys.projects, projectMap);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> updateProject(
    ProjectModel project,
    String userId,
  ) async {
    try {
      final projectMap = project.toJson();
      final currentProjects = UserDataService.getListData(LocalStorageKeys.projects);
      final existingProject = currentProjects.firstWhere(
        (p) => p['id'] == project.id,
        orElse: () => {},
      );
      if (existingProject.isEmpty) {
        return Left('Project not found for update');
      }
      final response = await SubabaseServices.updateMapInList(
        table: 'portfolio_data',
        fieldName: 'projects',
        matchColumn: 'userId',
        matchValue: userId,
        updateByKey: 'id',
        updateByValue: project.id ?? '',
        newMapData: projectMap,
      );

      if (response.status) {
        await UserDataService.updateItemInList(
          LocalStorageKeys.projects,
          project.id!,
          projectMap,
        );
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, bool>> deleteProject(
    String id,
    String userId,
  ) async {
    try {
      final response = await SubabaseServices.deleteMapFromList(
        table: ClassTables.portfolioData,
        fieldName: ClassTables.projects,
        matchColumn: 'userId',
        matchValue: userId,
        deleteByKey: 'id',
        deleteByValue: id,
      );
      log("response deleted SubabaseServices ::: ${response.status}");
      if (response.status) {
        await UserDataService.deleteItemFromList(LocalStorageKeys.projects, id);
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left(e.toString());
    }
  }

  static Future<Either<String, List<ProjectModel>>> getProjects({
    bool? refresh,
    String? userId,
  }) async {
    try {
      if (refresh == false || refresh == null) {
        final data = UserDataService.getListData(LocalStorageKeys.projects);
        if (data.isNotEmpty) {
          final projects = data.map((projectData) => ProjectModel.fromJson(projectData)).toList();
          return Right(projects);
        }
      }
      
      if (userId != null) {
        final response = await SubabaseServices.get(
          table: 'portfolio_data',
          filter: {'userId': userId},
        );
        
        if (response.status && response.data != null && response.data.isNotEmpty) {
          final portfolioData = response.data[0];
          final projectsList = portfolioData['projects'] as List<dynamic>? ?? [];
          final projects = projectsList
              .map((projectData) => ProjectModel.fromJson(projectData as Map<String, dynamic>))
              .toList();
          
          // Update local cache
          await UserDataService.updateListData(
            LocalStorageKeys.projects,
            projectsList.map((e) => e as Map<String, dynamic>).toList(),
          );
          
          return Right(projects);
        }
      }
      
      return Right([]);
    } catch (e) {
      return Left(e.toString());
    }
  }
}
