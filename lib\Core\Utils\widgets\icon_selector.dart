import 'package:devfolio/Core/resources/app_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../Features/Admin/Skills/Presentation/cubit/skills_cubit.dart';
import '../../resources/app_colors.dart';
import '../../resources/app_icon.dart';

class IconSelector extends StatelessWidget {
  final String? selectedIcon;

  const IconSelector({
    super.key,
    this.selectedIcon,
  });



  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const RangeMaintainingScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
      ),
      itemCount: AppList.availableIcons.length,
      itemBuilder: (context, index) {
        final icon = AppList.availableIcons[index];
        var isSelected = selectedIcon == icon ;
        return GestureDetector(
          onTap: () {
            context.read<SkillsCubit>().onIconSelected(icon);
          },
          
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Icon(
                AppIcon.getIconData(icon),
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
            ),
          ),
        );
      },
    );
  }
}
