import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter/material.dart';

import '../../resources/app_colors.dart';
import '../validators/password_validate.dart';
import 'PasswordVisibility/password_visibility_cubit.dart';
import 'build_text_field.dart';

class CustomPasswordTextFromField extends StatelessWidget {
  final TextEditingController controller;
  final String fieldId; // Add a unique ID for each field
  final bool showForgetMessage;
  final bool isLogin;

  final String hintText;

  const CustomPasswordTextFromField({
    super.key,
    required this.controller,
    required this.fieldId,
    this.showForgetMessage = true,
    required this.hintText,
    this.isLogin = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PasswordVisibilityCubit(),
      child: BlocBuilder<PasswordVisibilityCubit, Map<String, bool>>(
        builder: (context, state) {
          final isVisible = state[fieldId] ?? false;
          return BuildTextField(
            label: "Password",
            icon: Icons.lock_outline,
            suffixIcon: IconButton(
              icon: Icon(
                isVisible ? Icons.visibility : Icons.visibility_off,
                color: AppColors.textSecondary,
              ),
              onPressed: () {
                context
                    .read<PasswordVisibilityCubit>()
                    .togglePasswordVisibility(fieldId);
              },
            ),
            controller: controller,
            hint: hintText,
            isPassword: !isVisible,
            validator: (p0) =>
                validatePassword(isLogin, password: p0 ?? ""),
            keyboardType: TextInputType.visiblePassword,
          );
        },
      ),
    );
  }
}
