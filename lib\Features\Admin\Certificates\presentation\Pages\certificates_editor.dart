import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../Cubit/certificates_cubit.dart';
import '../Cubit/certificates_state.dart';

class CertificatesEditor extends StatefulWidget {
  const CertificatesEditor({super.key});

  @override
  State<CertificatesEditor> createState() => _CertificatesEditorState();
}

class _CertificatesEditorState extends State<CertificatesEditor> {
  @override
  void initState() {
    super.initState();
    context.read<CertificatesCubit>().loadCertificates();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: ResponsiveLayout.getScreenPadding(context),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: 'Certificates Management',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.read<CertificatesCubit>().startAddingNew();
                    },
                    child: const Text('Add Certificate'),
                  ),
                ],
              ),
              SizedBox(height: 20.h),

              // Content
              Expanded(
                child: BlocConsumer<CertificatesCubit, CertificatesState>(
                  listener: (context, state) {
                    if (state.errorMessage != null &&
                        state.errorMessage!.isNotEmpty) {
                      if (state.isSuccess == false) {
                        MessageWidget.show(
                          context,
                          type: MessageType.error,
                          message: state.errorMessage!,
                        );
                      }
                      if (state.isSuccess == true) {
                        MessageWidget.show(
                          context,
                          type: MessageType.success,
                          message: "Operation completed successfully",
                        );
                      }
                      context.read<CertificatesCubit>().resetSuccess();
                    }
                  },
                  builder: (context, state) {
                    if (state.isLoading && state.certificatesList.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    }

                    // Show form if editing or adding
                    if (state.editingCertificate?.id != null || state.isAddingNew ) {
                      return _buildCertificateForm(context, state);
                    }

                    // Show list of certificate entries
                    return _buildCertificatesList(context, state);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCertificateForm(BuildContext context, CertificatesState state) {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        children: [
          // Form header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text: state.isAddingNew
                    ? 'Add Certificate'
                    : 'Edit Certificate',
                fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              IconButton(
                onPressed: () {
                  context.read<CertificatesCubit>().cancelEditing();
                },
                icon: Icon(Icons.close, color: Colors.grey, size: 24.sp),
              ),
            ],
          ),
          SizedBox(height: 20.h),

          // Form content
          Expanded(child: _buildCertificateFormFields(context, state)),
        ],
      ),
    );
  }

  Widget _buildCertificateFormFields(
    BuildContext context,
    CertificatesState state,
  ) {
    final nameController = TextEditingController(
      text: state.editingCertificate?.name ?? '',
    );
    final issuingOrgController = TextEditingController(
      text: state.editingCertificate?.issuingOrganization ?? '',
    );
    final issueDateController = TextEditingController(
      text: state.editingCertificate?.issueDate ?? '',
    );
    final expiryDateController = TextEditingController(
      text: state.editingCertificate?.expiryDate ?? '',
    );
    final credentialIdController = TextEditingController(
      text: state.editingCertificate?.credentialId ?? '',
    );
    final descriptionController = TextEditingController(
      text: state.editingCertificate?.description ?? '',
    );

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTextField(
            nameController,
            'Certificate Name',
            Icons.card_membership,
          ),
          SizedBox(height: 15.h),
          _buildTextField(
            issuingOrgController,
            'Issuing Organization',
            Icons.business,
          ),
          SizedBox(height: 15.h),
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  issueDateController,
                  'Issue Date',
                  Icons.calendar_today,
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: _buildTextField(
                  expiryDateController,
                  'Expiry Date (Optional)',
                  Icons.calendar_today,
                ),
              ),
            ],
          ),
          SizedBox(height: 15.h),
          _buildTextField(
            credentialIdController,
            'Credential ID (Optional)',
            Icons.confirmation_number,
          ),
          SizedBox(height: 15.h),
          _buildTextField(
            descriptionController,
            'Description (Optional)',
            Icons.description,
            maxLines: 3,
          ),
          SizedBox(height: 30.h),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    context.read<CertificatesCubit>().cancelEditing();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.withAlpha(50),
                    foregroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: state.isLoading
                      ? null
                      : () {
                          context.read<CertificatesCubit>().saveCertificate(
                            name: nameController.text,
                            issuingOrganization: issuingOrgController.text,
                            issueDate: issueDateController.text,
                            expiryDate: expiryDateController.text.isEmpty
                                ? null
                                : expiryDateController.text,
                            credentialId: credentialIdController.text.isEmpty
                                ? null
                                : credentialIdController.text,
                            description: descriptionController.text.isEmpty
                                ? null
                                : descriptionController.text,
                          );
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                  child: state.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Save'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon, {
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 20.sp),
            SizedBox(width: 8.w),
            CustomText(
              text: label,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          style: TextStyle(
            color: Colors.white,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 15.w,
              vertical: 15.h,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCertificatesList(BuildContext context, CertificatesState state) {
    if (state.certificatesList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.card_membership, size: 64.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            CustomText(
              text: 'No certificates yet',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              color: Colors.grey,
            ),
            SizedBox(height: 8.h),
            CustomText(
              text: 'Click "Add Certificate" to get started',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(20.w),
      itemCount: state.certificatesList.length,
      itemBuilder: (context, index) {
        final certificate = state.certificatesList[index];
        return Container(
          margin: EdgeInsets.only(bottom: 15.h),
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.grey.withAlpha(30), width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with certificate name and actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: CustomText(
                      text: certificate.name ?? 'No Name',
                      fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          context.read<CertificatesCubit>().startEditing(
                            certificate,
                          );
                        },
                        icon: Icon(
                          Icons.edit,
                          color: AppColors.primary,
                          size: 20.sp,
                        ),
                      ),
                      IconButton(
                        onPressed: () async{
                         final result =await _showDeleteConfirmation(context, certificate);
                         if (result == true ) {
                          if (context.mounted) {
                            context.read<CertificatesCubit>().deleteCertificate(certificate);
                          }
                         }
                        },
                        icon: Icon(
                          Icons.delete,
                          color: Colors.red,
                          size: 20.sp,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10.h),

              // Issuing organization
              Row(
                children: [
                  Icon(Icons.business, color: AppColors.primary, size: 16.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: CustomText(
                      text:
                          certificate.issuingOrganization ?? 'No Organization',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),

              // Issue date
              SizedBox(height: 10.h),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: AppColors.primary,
                    size: 16.sp,
                  ),
                  SizedBox(width: 8.w),
                  CustomText(
                    text: 'Issued: ${certificate.issueDate ?? 'N/A'}',
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    color: Colors.grey,
                  ),
                ],
              ),

              // Expiry date if available
              if (certificate.expiryDate != null &&
                  certificate.expiryDate!.isNotEmpty) ...[
                SizedBox(height: 5.h),
                Row(
                  children: [
                    Icon(
                      Icons.event_busy,
                      color: AppColors.primary,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    CustomText(
                      text: 'Expires: ${certificate.expiryDate}',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.grey,
                    ),
                  ],
                ),
              ],

              // Credential ID if available
              if (certificate.credentialId != null &&
                  certificate.credentialId!.isNotEmpty) ...[
                SizedBox(height: 5.h),
                Row(
                  children: [
                    Icon(
                      Icons.confirmation_number,
                      color: AppColors.primary,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    CustomText(
                      text: 'ID: ${certificate.credentialId}',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.grey,
                    ),
                  ],
                ),
              ],

              // Description if available
              if (certificate.description != null &&
                  certificate.description!.isNotEmpty) ...[
                SizedBox(height: 10.h),
                CustomText(
                  text: certificate.description!,
                  fontSize: ResponsiveLayout.getBodyFontSize(context),
                  color: Colors.grey,
                  maxLines: 3,
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Future<bool> _showDeleteConfirmation(BuildContext context, certificate) async{
 return  await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: CustomText(
          text: 'Delete Certificate',
          fontSize: ResponsiveLayout.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        content: CustomText(
          text:
              'Are you sure you want to delete this certificate? This action cannot be undone.',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
          color: Colors.grey,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: CustomText(
              text: 'Cancel',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
