import 'package:flutter_test/flutter_test.dart';
import 'package:devfolio/Core/models/education_model.dart';
import 'package:devfolio/Core/models/experience_model.dart';
import 'package:devfolio/Core/models/certificate_model.dart';
import 'package:devfolio/Features/Admin/Education/presentation/Cubit/education_state.dart';
import 'package:devfolio/Features/Admin/Experience/presentation/Cubit/experience_state.dart';
import 'package:devfolio/Features/Admin/Certificates/presentation/Cubit/certificates_state.dart';

void main() {
  group('EducationModel Tests', () {
    test('should create EducationModel with all fields', () {
      final education = EducationModel(
        id: '1',
        degree: 'Bachelor of Science',
        institution: 'University of Technology',
        startDate: '2020-09',
        endDate: '2024-06',
        description: 'Computer Science degree',
        location: 'New York, NY',
        gpa: '3.8',
      );

      expect(education.id, '1');
      expect(education.degree, 'Bachelor of Science');
      expect(education.institution, 'University of Technology');
      expect(education.startDate, '2020-09');
      expect(education.endDate, '2024-06');
      expect(education.description, 'Computer Science degree');
      expect(education.location, 'New York, NY');
      expect(education.gpa, '3.8');
    });

    test('should create EducationModel from JSON with Supabase format', () {
      final json = {
        'id': '1',
        'degree': 'Bachelor of Science',
        'institution': 'University of Technology',
        'start_date': '2020-09', // Supabase format
        'end_date': '2024-06', // Supabase format
        'description': 'Computer Science degree',
        'location': 'New York, NY',
        'gpa': '3.8',
      };

      final education = EducationModel.fromJson(json);

      expect(education.startDate, '2020-09');
      expect(education.endDate, '2024-06');
    });

    test('should create copy with updated fields', () {
      final education = EducationModel(
        id: '1',
        degree: 'Bachelor of Science',
        institution: 'University of Technology',
      );

      final updatedEducation = education.copyWith(
        degree: 'Master of Science',
        gpa: '3.9',
      );

      expect(updatedEducation.id, '1');
      expect(updatedEducation.degree, 'Master of Science');
      expect(updatedEducation.institution, 'University of Technology');
      expect(updatedEducation.gpa, '3.9');
    });

    test('should generate time-based ID', () async {
      final id1 = EducationModel.generateTimeIdString();
      await Future.delayed(Duration(milliseconds: 1));
      final id2 = EducationModel.generateTimeIdString();

      expect(id1, isNotEmpty);
      expect(id2, isNotEmpty);
      expect(id1, isNot(equals(id2)));
    });
  });

  group('ExperienceModel Tests', () {
    test('should create ExperienceModel with all fields', () {
      final experience = ExperienceModel(
        id: '1',
        company: 'Tech Corp',
        position: 'Software Engineer',
        startDate: '2022-01',
        endDate: '2024-01',
        description: 'Developed mobile applications',
        location: 'San Francisco, CA',
        technologies: ['Flutter', 'Dart', 'Firebase'],
      );

      expect(experience.id, '1');
      expect(experience.company, 'Tech Corp');
      expect(experience.position, 'Software Engineer');
      expect(experience.technologies, ['Flutter', 'Dart', 'Firebase']);
    });

    test('should create ExperienceModel from JSON with different formats', () {
      final json = {
        'id': '1',
        'companyName': 'Tech Corp', // Alternative format
        'jobTitle': 'Software Engineer', // Alternative format
        'start_date': '2022-01',
        'end_date': '2024-01',
        'description': 'Developed mobile applications',
        'location': 'San Francisco, CA',
        'technologies': ['Flutter', 'Dart'],
      };

      final experience = ExperienceModel.fromJson(json);

      expect(experience.company, 'Tech Corp');
      expect(experience.position, 'Software Engineer');
    });
  });

  group('CertificateModel Tests', () {
    test('should create CertificateModel with all fields', () {
      final certificate = CertificateModel(
        id: '1',
        name: 'AWS Certified Developer',
        issuingOrganization: 'Amazon Web Services',
        issueDate: '2023-06',
        expiryDate: '2026-06',
        credentialId: 'AWS-123456',
        description: 'Cloud development certification',
      );

      expect(certificate.id, '1');
      expect(certificate.name, 'AWS Certified Developer');
      expect(certificate.issuingOrganization, 'Amazon Web Services');
      expect(certificate.credentialId, 'AWS-123456');
    });

    test('should create CertificateModel from JSON with different formats', () {
      final json = {
        'id': '1',
        'name': 'AWS Certified Developer',
        'issuing_organization': 'Amazon Web Services', // Supabase format
        'issue_date': '2023-06', // Supabase format
        'expiry_date': '2026-06', // Supabase format
        'credential_id': 'AWS-123456', // Supabase format
        'description': 'Cloud development certification',
      };

      final certificate = CertificateModel.fromJson(json);

      expect(certificate.issuingOrganization, 'Amazon Web Services');
      expect(certificate.issueDate, '2023-06');
      expect(certificate.expiryDate, '2026-06');
      expect(certificate.credentialId, 'AWS-123456');
    });
  });

  group('State Management Tests', () {
    test('should create initial EducationState', () {
      final state = EducationState();

      expect(state.educationList, isEmpty);
      expect(state.editingEducation, isNull);
      expect(state.isAddingNew, false);
      expect(state.isLoading, false);
      expect(state.errorMessage, isNull);
      expect(state.hasChanges, false);
      expect(state.isSuccess, isNull);
    });

    test('should copy EducationState with updated fields', () {
      final initialState = EducationState();
      final education = EducationModel(id: '1', degree: 'Test');

      final updatedState = initialState.copyWith(
        educationList: [education],
        isLoading: true,
        errorMessage: 'Test error',
        isSuccess: true,
      );

      expect(updatedState.educationList, [education]);
      expect(updatedState.isLoading, true);
      expect(updatedState.errorMessage, 'Test error');
      expect(updatedState.isSuccess, true);
    });

    test('should create initial ExperienceState', () {
      final state = ExperienceState();

      expect(state.experienceList, isEmpty);
      expect(state.editingExperience, isNull);
      expect(state.isAddingNew, false);
      expect(state.isLoading, false);
      expect(state.errorMessage, isNull);
      expect(state.hasChanges, false);
      expect(state.isSuccess, isNull);
    });

    test('should create initial CertificatesState', () {
      final state = CertificatesState();

      expect(state.certificatesList, isEmpty);
      expect(state.editingCertificate, isNull);
      expect(state.isAddingNew, false);
      expect(state.isLoading, false);
      expect(state.errorMessage, isNull);
      expect(state.hasChanges, false);
      expect(state.isSuccess, isNull);
    });
  });
}
