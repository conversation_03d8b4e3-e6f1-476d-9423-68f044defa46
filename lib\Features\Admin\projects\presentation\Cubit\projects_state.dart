import '../../../../../Core/models/project_model.dart';

class ProjectsState {
  final List<ProjectModel> projectsList;
  final ProjectModel? editingProject;
  final bool isAddingNew;
  final bool isLoading;
  final String? errorMessage;
  final bool hasChanges;
  final bool? isSuccess;
  final bool isUploadingImage;

  ProjectsState({
    this.projectsList = const [],
    this.editingProject,
    this.isAddingNew = false,
    this.isLoading = false,
    this.errorMessage,
    this.hasChanges = false,
    this.isSuccess,
    this.isUploadingImage = false,
  });

  ProjectsState copyWith({
    List<ProjectModel>? projectsList,
    ProjectModel? editingProject,
    bool? isAddingNew,
    bool? isLoading,
    String? errorMessage,
    bool? hasChanges,
    bool? isSuccess,
    bool? isUploadingImage,
  }) {
    return ProjectsState(
      projectsList: projectsList ?? this.projectsList,
      editingProject: editingProject ?? this.editingProject,
      isAddingNew: isAddingNew ?? this.isAddingNew,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      hasChanges: hasChanges ?? this.hasChanges,
      isSuccess: isSuccess ?? this.isSuccess,
      isUploadingImage: isUploadingImage ?? this.isUploadingImage,
    );
  }
}
