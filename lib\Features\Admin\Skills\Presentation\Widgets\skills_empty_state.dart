import '../../../../../Core/Utils/Reusable/custom_button.dart';
import '../../../../../Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class SkillsEmptyState extends StatelessWidget {
  final VoidCallback onAddSkill;

  const SkillsEmptyState({super.key, required this.onAddSkill});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(30.w),
            decoration: BoxDecoration(
              color: AppColors.withOpacity(AppColors.textSecondary, 0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(
              Icons.psychology_outlined,
              size: 80.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 20.h),
          CustomText(
            text: AppStrings.noSkillsYet,
            fontSize: ResponsiveLayout.getSubtitleFontSize(context),
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: 10.h),
          CustomText(
            text: AppStrings.addFirstSkill,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
            color: AppColors.textSecondary,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          CustomButton(
            text: AppStrings.addNewSkill,
            onPressed: onAddSkill,
            backgroundColor: AppColors.primary,
          ),
        ],
      ),
    );
  }
}
