import 'package:devfolio/Core/Utils/Reusable/about_point.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:devfolio/Core/Utils/Reusable/skill_chip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../Core/layout/responsive_layout.dart';
import '../../config/cubit/portfolio_cubit.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'About Me',
      child: BlocBuilder<PortfolioCubit, PortfolioState>(
        builder: (context, state) {
          final portfolioData = state.portfolioData;
          final aboutText =
              portfolioData?.personalInfo?.about ??
              'I am a passionate developer with experience in creating amazing applications.';

          return SingleChildScrollView(
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // About Description
                CustomText(
                  text: aboutText,
                  fontSize: ResponsiveLayout.getBodyFontSize(context),
                  color: Colors.grey,
                ),

                SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

                // Key Points
                AboutPoint(
                  title: 'Mobile Development',
                  description:
                      'Expert in Flutter and Dart for cross-platform mobile development',
                  icon: Icons.phone_android,
                ),

                SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

                AboutPoint(
                  title: 'UI/UX Design',
                  description:
                      'Creating beautiful and intuitive user interfaces with modern design principles',
                  icon: Icons.design_services,
                ),

                SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

                AboutPoint(
                  title: 'Problem Solving',
                  description:
                      'Strong analytical skills to solve complex technical challenges',
                  icon: Icons.lightbulb,
                ),

                SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

                // Skills Preview
                CustomText(
                  text: 'Key Skills',
                  fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                  fontWeight: FontWeight.bold,
                ),

                SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),

                Wrap(
                  spacing: ResponsiveLayout.getSmallSpacing(context),
                  runSpacing: ResponsiveLayout.getSmallSpacing(context),
                  children: [
                    SkillChip(skill: 'Flutter'),
                    SkillChip(skill: 'Dart'),
                    SkillChip(skill: 'Firebase'),
                    SkillChip(skill: 'REST APIs'),
                    SkillChip(skill: 'Git'),
                    SkillChip(skill: 'UI/UX'),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
