class SocialLinks {
  final String? github;
  final String? linkedin;
  final String? twitter;
  final String? instagram;
  final String? facebook;
  final String? youtube;
  final String? tiktok;
  final String? twitch;
  final String? discord;
  final String? telegram;
  final String? whatsapp;
  final String? medium;
  final String? stackoverflow;
  final String? devto;
  final String? hashnode;
  final String? gitlab;
  final String? bitbucket;
  final String? codewars;
  final String? codepen;

  SocialLinks({
    this.github,
    this.linkedin,
    this.twitter,
    this.instagram,
    this.facebook,
    this.youtube,
    this.tiktok,
    this.twitch,
    this.discord,
    this.telegram,
    this.whatsapp,
    this.medium,
    this.stackoverflow,
    this.devto,
    this.hashnode,
    this.gitlab,
    this.bitbucket,
    this.codewars,
    this.codepen,
  });

  factory SocialLinks.fromJson(Map<String, dynamic> json) {
    return SocialLinks(
      github: json['github'] ?? '',
      linkedin: json['linkedin'] ?? '',
      twitter: json['twitter'] ?? '',
      instagram: json['instagram'] ?? '',
      facebook: json['facebook'] ?? '',
      youtube: json['youtube'] ?? '',
      tiktok: json['tiktok'] ?? '',
      twitch: json['twitch'] ?? '',
      discord: json['discord'] ?? '',
      telegram: json['telegram'] ?? '',
      whatsapp: json['whatsapp'],
      medium: json['medium'] ?? '',
      stackoverflow: json['stackoverflow'] ?? '',
      devto: json['devto'] ?? '',
      hashnode: json['hashnode'] ?? '',
      gitlab: json['gitlab'] ?? '',
      bitbucket: json['bitbucket'] ?? '',
      codewars: json['codewars'] ?? '',
      codepen: json['codepen'] ?? '',
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'github': github,
      'linkedin': linkedin,
      'twitter': twitter,
      'instagram': instagram,
      'facebook': facebook,
      'youtube': youtube,
      'tiktok': tiktok,
      'twitch': twitch,
      'discord': discord,
      'telegram': telegram,
      'whatsapp': whatsapp,
      'medium': medium,
      'stackoverflow': stackoverflow,
      'devto': devto,
      'hashnode': hashnode,
      'gitlab': gitlab,
      'bitbucket': bitbucket,
      'codewars': codewars,
      'codepen': codepen,
    };
  }
  static SocialLinks empty() {
    return SocialLinks(
      github: '',
      linkedin: '',
      twitter: '',
      instagram: '',
      facebook: '',
      youtube: '',
      tiktok: '',
      twitch: '',
      discord: '',
      telegram: '',
      whatsapp: '',
      medium: '',
      stackoverflow: '',
      devto: '',
      hashnode: '',
      gitlab: '',
      bitbucket: '',
      codewars: '',
      codepen: '',
    );
  }
}
