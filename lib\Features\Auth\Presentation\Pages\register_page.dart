import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../Core/Utils/widgets/animated_background.dart';
import '../../../../Core/Utils/widgets/message_widget.dart';
import '../../../../Core/layout/responsive_layout.dart';
import '../../../../main.dart';
import '../../Data/Cubit/auth_cubit.dart';
import '../../Data/Cubit/auth_state.dart';
import '../Widgets/PartsRegister/register_page.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<AuthCubit>().clearState();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: BlocListener<AuthCubit, AuthState>(
          listener: (context, state) {
            if (state.isSuccess == true) {
              MessageWidget.show(
                context,
                type: MessageType.success,
                message: state.message ?? 'Register successful',
              );
              // Navigate to dashboard with user email in URL
              kNavigationService.navigateToDashboardWithUser();
            } else if (state.isSuccess == false) {
              MessageWidget.show(
                context,
                type: MessageType.error,
                message: state.message ?? 'Register failed',
              );
            }
          },
          child: ResponsiveLayout.isMobile(context)
              ? _buildMobileLayout()
              : _buildLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return CustomScrollView(
      physics: const RangeMaintainingScrollPhysics(),
      slivers: [
        SliverPadding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          sliver: SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: true,
            child: _buildLayout(),
          ),
        ),
      ],
    );
  }

  Widget _buildLayout() {
    return Center(
      child: _buildAuthCard()
          .animate()
          .fadeIn(duration: 800.ms)
          .scale(begin: const Offset(0.9, 0.9)),
    );
  }

  Widget _buildAuthCard() {
    return BuildAuthCardRegister(
      context: context,
      formKey: _formKey,
      nameController: _nameController,
      emailController: _emailController,
      phoneController: _phoneController,
      passwordController: _passwordController,
    );
  }
}
