import 'package:devfolio/Features/Admin/Personal/Presentation/cubit/personal_contact_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/models/personal_info_model.dart';
import '../../../../../Core/resources/resources.dart';
import '../cubit/personal_contact_cubit.dart';
import 'build_additional_info_tab.dart';
import 'build_contact_info_tab.dart';
import 'build_personal_info_tab.dart';
import 'build_social_media_tab.dart';

class PersonalTabContent extends StatefulWidget {
  final PersonalContactState state;
  final Function(String, String) onDataUpdate;

  const PersonalTabContent({
    super.key,
    required this.state,
    required this.onDataUpdate,
  });

  @override
  State<PersonalTabContent> createState() => _PersonalTabContentState();
}

class _PersonalTabContentState extends State<PersonalTabContent> {
  final Map<String, TextEditingController> _controllers = {};

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(PersonalTabContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update controllers when data changes
    if (widget.state.personalData != oldWidget.state.personalData) {
      _updateControllers();
    }
  }

  void _updateControllers() {
    if (widget.state.personalData != null) {
      for (var entry in _controllers.entries) {
        final key = entry.key;
        final controller = entry.value;
        final value = _getValueFromPersonalData(
          widget.state.personalData!,
          key,
        );
        final newText = value ?? '';
        if (controller.text != newText) {
          // Update without triggering listener
          controller.value = controller.value.copyWith(
            text: newText,
            selection: TextSelection.collapsed(offset: newText.length),
          );
        }
      }
    }
  }

  TextEditingController _getController(String key) {
    if (!_controllers.containsKey(key)) {
      final cubit = context.read<PersonalContactCubit>();
      final controller = TextEditingController();

      // Set initial value from database data
      if (cubit.state.personalData != null) {
        final value = _getValueFromPersonalData(cubit.state.personalData!, key);
        controller.text = value ?? '';
      }

      controller.addListener(() {
        cubit.updateField(key, controller.text);
        widget.onDataUpdate(key, controller.text);
      });

      _controllers[key] = controller;
    }
    return _controllers[key]!;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: AppConstants.mediumAnimation,
      child: widget.state.currentTab == 0
          ? BuildPersonalInfoTab(
              nameController: _getController('name'),
              titleController: _getController('title'),
              aboutController: _getController('about'),
            )
          : widget.state.currentTab == 1
          ? BuildContactInfoTab(
              emailController: _getController('email'),
              phoneController: _getController('phone'),
              locationController: _getController('location'),
              websiteController: _getController('website'),
              linkedinController: _getController('linkedin'),
              githubController: _getController('github'),
            )
          : widget.state.currentTab == 2
          ? BuildSocialMediaTab(
              twitterController: _getController('twitter'),
              instagramController: _getController('instagram'),
              facebookController: _getController('facebook'),
              youtubeController: _getController('youtube'),
            )
          : BuildAdditionalInfoTab(
              dateOfBirthController: _getController('dateOfBirth'),
              nationalityController: _getController('nationality'),
              languagesController: _getController('languages'),
              interestsController: _getController('interests'),
            ),
    );
  }

  String? _getValueFromPersonalData(
    PersonalInfoModel personalData,
    String key,
  ) {
    switch (key) {
      case 'name':
        return personalData.name;
      case 'title':
        return personalData.title;
      case 'about':
        return personalData.about;
      case 'email':
        return personalData.email;
      case 'phone':
        return personalData.phone;
      case 'location':
        return personalData.location;
      case 'website':
        return personalData.website;
      case 'linkedin':
        return personalData.linkedin;
      case 'github':
        return personalData.github;
      case 'twitter':
        return personalData.twitter;
      case 'instagram':
        return personalData.instagram;
      case 'facebook':
        return personalData.facebook;
      case 'youtube':
        return personalData.youtube;
      case 'dateOfBirth':
        return personalData.dateOfBirth;
      case 'nationality':
        return personalData.nationality;
      case 'languages':
        return personalData.languages;
      case 'interests':
        return personalData.interests;
      default:
        return null;
    }
  }
}
