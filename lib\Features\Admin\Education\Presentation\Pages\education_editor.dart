import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../Core/Utils/widgets/message_widget.dart';
import '../Cubit/education_cubit.dart';
import '../Cubit/education_state.dart';

class EducationEditor extends StatefulWidget {
  const EducationEditor({super.key});

  @override
  State<EducationEditor> createState() => _EducationEditorState();
}

class _EducationEditorState extends State<EducationEditor> {
  @override
  void initState() {
    super.initState();
    context.read<EducationCubit>().loadEducation();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: ResponsiveLayout.getScreenPadding(context),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: 'Education Management',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      context.read<EducationCubit>().startAddingNew();
                    },
                    child: const Text('Add Education'),
                  ),
                ],
              ),
              SizedBox(height: 20.h),

              // Content
              Expanded(
                child: BlocConsumer<EducationCubit, EducationState>(
                  listener: (context, state) {
                    if (state.errorMessage != null &&
                        state.errorMessage!.isNotEmpty) {
                      if (state.isSuccess == false) {
                        MessageWidget.show(
                          context,
                          type: MessageType.error,
                          message: state.errorMessage!,
                        );
                      }
                      if (state.isSuccess == true) {
                        MessageWidget.show(
                          context,
                          type: MessageType.success,
                          message: "Operation completed successfully",
                        );
                      }
                      context.read<EducationCubit>().resetSuccess();
                    }
                  },
                  builder: (context, state) {
                    if (state.isLoading && state.educationList.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: AppColors.primary,
                        ),
                      );
                    }

                    // Show form if editing or adding
                    if (state.editingEducation?.id != null || state.isAddingNew == true) {
                      return _buildEducationForm(context, state);
                    }

                    // Show list of education entries
                    return _buildEducationList(context, state);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEducationForm(BuildContext context, EducationState state) {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        children: [
          // Form header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text: state.isAddingNew ? 'Add Education' : 'Edit Education',
                fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              IconButton(
                onPressed: () {
                  context.read<EducationCubit>().cancelEditing();
                },
                icon: Icon(Icons.close, color: Colors.grey, size: 24.sp),
              ),
            ],
          ),
          SizedBox(height: 20.h),

          // Form content
          Expanded(child: _buildFormFields(context, state)),
        ],
      ),
    );
  }

  Widget _buildFormFields(BuildContext context, EducationState state) {
    final degreeController = TextEditingController(
      text: state.editingEducation?.degree ?? '',
    );
    final institutionController = TextEditingController(
      text: state.editingEducation?.institution ?? '',
    );
    final startDateController = TextEditingController(
      text: state.editingEducation?.startDate ?? '',
    );
    final endDateController = TextEditingController(
      text: state.editingEducation?.endDate ?? '',
    );
    final descriptionController = TextEditingController(
      text: state.editingEducation?.description ?? '',
    );
    final locationController = TextEditingController(
      text: state.editingEducation?.location ?? '',
    );
    final gpaController = TextEditingController(
      text: state.editingEducation?.gpa ?? '',
    );

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTextField(degreeController, 'Degree/Program', Icons.school),
          SizedBox(height: 15.h),
          _buildTextField(institutionController, 'Institution', Icons.business),
          SizedBox(height: 15.h),
          Row(
            children: [
              Expanded(
                child: _buildTextField(
                  startDateController,
                  'Start Date',
                  Icons.calendar_today,
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: _buildTextField(
                  endDateController,
                  'End Date',
                  Icons.calendar_today,
                ),
              ),
            ],
          ),
          SizedBox(height: 15.h),
          _buildTextField(locationController, 'Location', Icons.location_on),
          SizedBox(height: 15.h),
          _buildTextField(gpaController, 'GPA (Optional)', Icons.grade),
          SizedBox(height: 15.h),
          _buildTextField(
            descriptionController,
            'Description',
            Icons.description,
            maxLines: 3,
          ),
          SizedBox(height: 30.h),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    context.read<EducationCubit>().cancelEditing();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.withAlpha(50),
                    foregroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
              ),
              SizedBox(width: 15.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: state.isLoading
                      ? null
                      : () {
                          context.read<EducationCubit>().saveEducation(
                            degree: degreeController.text,
                            institution: institutionController.text,
                            startDate: startDateController.text,
                            endDate: endDateController.text,
                            description: descriptionController.text,
                            location: locationController.text,
                            gpa: gpaController.text,
                          );
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                  ),
                  child: state.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Save'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon, {
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 20.sp),
            SizedBox(width: 8.w),
            CustomText(
              text: label,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ],
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          style: TextStyle(
            color: Colors.white,
            fontSize: ResponsiveLayout.getBodyFontSize(context),
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey.withAlpha(30)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 15.w,
              vertical: 15.h,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEducationList(BuildContext context, EducationState state) {
    if (state.educationList.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school, size: 64.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            CustomText(
              text: 'No education entries yet',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              color: Colors.grey,
            ),
            SizedBox(height: 8.h),
            CustomText(
              text: 'Click "Add Education" to get started',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(20.w),
      itemCount: state.educationList.length,
      itemBuilder: (context, index) {
        final education = state.educationList[index];
        return Container(
          margin: EdgeInsets.only(bottom: 15.h),
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.grey.withAlpha(30), width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with degree and actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: CustomText(
                      text: education.degree ?? 'No Degree',
                      fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          context.read<EducationCubit>().startEditing(
                            education,
                          );
                        },
                        icon: Icon(
                          Icons.edit,
                          color: AppColors.primary,
                          size: 20.sp,
                        ),
                      ),
                      IconButton(
                        onPressed: ()async {
                         final result = await _showDeleteConfirmation(context, education);
                         if (result == true) {
                          if (context.mounted) {
                            context.read<EducationCubit>().deleteEducation(education);
                          }
                         }
                        },
                        icon: Icon(
                          Icons.delete,
                          color: Colors.red,
                          size: 20.sp,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 10.h),

              // Institution and location
              Row(
                children: [
                  Icon(Icons.business, color: AppColors.primary, size: 16.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: CustomText(
                      text: education.institution ?? 'No Institution',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              if (education.location != null &&
                  education.location!.isNotEmpty) ...[
                SizedBox(height: 5.h),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: AppColors.primary,
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    CustomText(
                      text: education.location!,
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.grey,
                    ),
                  ],
                ),
              ],

              // Dates
              SizedBox(height: 10.h),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: AppColors.primary,
                    size: 16.sp,
                  ),
                  SizedBox(width: 8.w),
                  CustomText(
                    text:
                        '${education.startDate ?? 'N/A'} - ${education.endDate ?? 'N/A'}',
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    color: Colors.grey,
                  ),
                ],
              ),

              // GPA if available
              if (education.gpa != null && education.gpa!.isNotEmpty) ...[
                SizedBox(height: 5.h),
                Row(
                  children: [
                    Icon(Icons.grade, color: AppColors.primary, size: 16.sp),
                    SizedBox(width: 8.w),
                    CustomText(
                      text: 'GPA: ${education.gpa}',
                      fontSize: ResponsiveLayout.getBodyFontSize(context),
                      color: Colors.grey,
                    ),
                  ],
                ),
              ],

              // Description if available
              if (education.description != null &&
                  education.description!.isNotEmpty) ...[
                SizedBox(height: 10.h),
                CustomText(
                  text: education.description!,
                  fontSize: ResponsiveLayout.getBodyFontSize(context),
                  color: Colors.grey,
                  maxLines: 3,
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Future<bool > _showDeleteConfirmation(BuildContext context, education) async{
    return await  showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: CustomText(
          text: 'Delete Education',
          fontSize: ResponsiveLayout.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        content: CustomText(
          text:
              'Are you sure you want to delete this education entry? This action cannot be undone.',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
          color: Colors.grey,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: CustomText(
              text: 'Cancel',
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
