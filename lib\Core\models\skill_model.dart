class SkillModel {
  final String? id;
  final String? name;
  final String? category;
  final String? icon;

  SkillModel({this.id, this.name, this.category, this.icon});

  factory SkillModel.fromJson(Map<String, dynamic> json) {
    return SkillModel(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      icon: json['icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (category != null) 'category': category,
      if (icon != null) 'icon': icon,
    };
  }

  SkillModel copyWith({
    String? id,
    String? name,
    String? category,
    String? icon,
  }) {
    return SkillModel(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      icon: icon ?? this.icon,
    );
  }

  static String generateTimeIdString() {
    return (DateTime.now().millisecondsSinceEpoch % 1000000).toString();
  }
}
