{"personalInfo": {"name": "Your Name", "title": "Flutter Developer", "description": "Passionate Flutter developer with experience in building beautiful and responsive applications.", "email": "<EMAIL>", "phone": "+1234567890", "location": "Your City, Country", "github": "https://github.com/yourusername", "linkedin": "https://linkedin.com/in/yourusername", "website": "https://yourwebsite.com", "avatar": "assets/images/avatar.jpg", "about": "I am a dedicated Flutter developer with a passion for creating beautiful, responsive, and user-friendly applications. I have experience in both mobile and web development using Flutter framework."}, "skills": [{"id": "1", "name": "Flutter", "category": "Framework", "icon": "flutter"}], "experience": [{"id": "1", "company": "Tech Company", "position": "Senior Flutter Developer", "startDate": "2023-01", "endDate": "Present", "description": "Led development of multiple Flutter applications, mentored junior developers, and implemented best practices.", "technologies": ["Flutter", "Dart", "Firebase", "Git"], "location": "Remote"}], "certifications": [{"id": "1", "name": "Certification Name", "description": "Certification Description", "image": "assets/images/certification1.jpg", "link": "https://certification1.com", "dateTime": "2025-01-01 12:00:00"}], "projects": [{"id": "1", "title": "E-Commerce App", "description": "A full-featured e-commerce mobile application with payment integration and admin panel.", "image": "assets/images/project1.jpg", "technologies": ["Flutter", "Firebase", "Stripe"], "github": "https://github.com/yourusername/ecommerce-app", "liveUrl": "https://ecommerce-app.com", "category": "Mobile App"}], "education": [{"id": "1", "degree": "Bachelor of Computer Science", "institution": "University Name", "startDate": "2018-09", "endDate": "2022-06", "description": "Studied computer science with focus on software development and mobile applications.", "location": "City, Country", "gpa": "3.8"}], "sendMessage": {"email": "<EMAIL>", "message": "Your message here", "name": "Your Name", "dateTime": "2025-01-01 12:00:00"}}