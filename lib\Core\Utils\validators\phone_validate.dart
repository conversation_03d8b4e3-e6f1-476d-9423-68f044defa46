String? validatePhone(
  String phone, {
  bool isRtl = false,
}) {
  const String pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
  final RegExp regex = RegExp(pattern);
  // Asynchronous Firebase Firestore check for existing phone number
  // final FirebaseFirestore firestore = FirebaseFirestore.instance;
  // final CollectionReference usersCollection = firestore.collection('Users');
  // final QuerySnapshot querySnapshot =
  //     await usersCollection.where('phone', isEqualTo: phone).get();
  if (phone.isEmpty) {
    return isRtl ? "الرجاء إدخال رقم الهاتف" : "Please Enter Your Phone";
  }
  // else  if (phone.length>=10 && phone.length<=13) {
  //   return '';
  // }
  else if (!regex.hasMatch(phone)) {
    return isRtl
        ? 'رقم الهاتف غير صحيح. الرجاء إدخال رقم هاتف صحيح مكون من 10-12 رقم.'
        : 'The phone number is invalid. Please enter a valid 10-12 digit phone number.';
  }
  // else if (querySnapshot.docs.isNotEmpty) {
  //   return isRtl
  //       ? 'رقم الهاتف مسجل بالفعل.'
  //       : 'The phone number is already registered.';
  // }
  return null;
}