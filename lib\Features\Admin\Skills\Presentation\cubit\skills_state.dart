
import '../../../../../Core/models/skill_model.dart';

class SkillsState {
  final List<SkillModel> skillsList;
  final SkillModel? editingSkill;
  final bool isAddingNew;
  final bool isEditing;
  final bool isLoading;
  final String? errorMessage;
  final String? onIconSelected;
  final bool? isSuccess;

  SkillsState({
    this.skillsList = const [],
    this.editingSkill,
    this.isAddingNew = true,
    this.isLoading = false,
    this.errorMessage,
    this.isEditing = false,
    this.onIconSelected ,
    this.isSuccess,
  });

  SkillsState copyWith({
    List<SkillModel>? skillsList,
    SkillModel? editingSkill,
    bool? isAddingNew,
    bool? isLoading,
    String? errorMessage,
    bool? isEditing,
    String? onIconSelected,
    bool? isSuccess,
  }) {
    return SkillsState(
      skillsList: skillsList ?? this.skillsList,
      editingSkill: editingSkill ?? this.editingSkill,
      isAddingNew: isAddingNew ?? this.isAddingNew,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      isEditing: isEditing ?? this.isEditing,
      onIconSelected: onIconSelected ?? this.onIconSelected,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
}
