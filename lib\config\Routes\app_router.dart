import 'package:dartz/dartz.dart';
import 'package:devfolio/Core/Utils/widgets/build_loading.dart';
import 'package:devfolio/Core/models/portfolio_data_model.dart';
import 'package:devfolio/Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import 'package:devfolio/Features/Auth/Presentation/Pages/login_page.dart';
import 'package:devfolio/Features/Auth/Presentation/Pages/register_page.dart';
import 'package:devfolio/Features/Portfolio/portfolio_main.dart';
import 'package:devfolio/config/Routes/app_routes.dart';
import 'package:devfolio/config/Routes/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../Core/services/user_verification_service.dart';

/// GoRouter Configuration - Clean and Optimized
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: RouteNames.splash,
    navigatorKey: rootNavigatorKey,
    routes: [
      // Root route - redirects based on authentication
      GoRoute(
        path: RouteNames.splash,
        name: RouteNames.splashName,
        builder: (context, state) =>
            Scaffold(body: Center(child: buildLoading())),
      ),

      // Static routes
      GoRoute(
        path: RouteNames.login,
        name: RouteNames.loginName,
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: RouteNames.register,
        name: RouteNames.registerName,
        builder: (context, state) => const RegisterPage(),
      ),

      // Dynamic routes with username parameter
      GoRoute(
        path: RouteNames.dashboardWithUser,
        name: RouteNames.dashboardWithUserName,
        builder: (context, state) {
          final username = state.pathParameters['username']!;
          return FutureBuilder<bool>(
            future: UserDataService.hasUserData(username),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Scaffold(body: Center(child: buildLoading()));
              }
              if (snapshot.hasError || !snapshot.hasData) {
                return const LoginPage();
              }
              if (snapshot.data == true) {
                return AdminDashboard();
              } else {
                return const LoginPage();
              }
            },
          );
        },
      ),

      // Direct username routes
      GoRoute(
        path: RouteNames.directUsername,
        name: RouteNames.directUsernameName,
        builder: (context, state) {
          final username = state.pathParameters['username']!;
          return FutureBuilder<Either<String, PortfolioDataModel?>>(
            future: UserVerificationService.checkUserByUsername(username),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Scaffold(body: Center(child: buildLoading()));
              }
              if (snapshot.hasError || !snapshot.hasData) {
                return const LoginPage();
              }
              return snapshot.data!.fold(
                (error) {
                  return const LoginPage();
                },
                (data) {
                  if (data == null) {
                    return const LoginPage();
                  }
                  return PortfolioMain(email: username, portfolioData: data);
                },
              );
            },
          );
        },
      ),
    ],
    // Error handling - fallback to login
    errorBuilder: (context, state) => const LoginPage(),

    redirect: (context, state) {
      // Handle splash route redirect
      final routes = RouteNames.staticRoutes;
      final myRoutes = routes.any((routes) => routes == state.fullPath);
      if (state.matchedLocation == RouteNames.splash || !myRoutes) {
        return AppRoutes.getInitialRoute();
      }
      return null;
    },
  );
}
