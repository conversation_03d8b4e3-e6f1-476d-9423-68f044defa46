import 'package:devfolio/Core/resources/resources.dart';
import 'package:devfolio/Core/Utils/widgets/build_text_field.dart';
import 'package:devfolio/Features/Admin/Personal/Presentation/Widget/build_section_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildPersonalInfoTab extends StatelessWidget {
  const BuildPersonalInfoTab({
    super.key,
    required this.nameController,
    required this.titleController,
    required this.aboutController,
  });

  final TextEditingController nameController;
  final TextEditingController titleController;
  final TextEditingController aboutController;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BuildSectionTitle(title:AppStrings.basicInformation),
          <PERSON>zed<PERSON><PERSON>(height: 15.h),
          BuildTextField(
            controller: nameController,
            label: AppStrings.fullName,
            icon: Icons.person,
            hint: AppStrings.enterFullName,
          ),
          <PERSON>zed<PERSON><PERSON>(height: 15.h),
          BuildTextField(
            controller: titleController,
            label: AppStrings.professionalTitle,
            icon: Icons.work,
            hint: AppStrings.flutterDeveloperExample,
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: aboutController,
            label: AppStrings.shortDescription,
            icon: Icons.description,
            maxLines: 3,
            hint: 'Brief description about yourself...',
          ),
          
        ],
      ),
    );
  }
}
