import 'package:devfolio/Core/Utils/Reusable/custom_icon.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AdminHeader extends StatelessWidget {
  final String title;
  final String currentSection;
  final bool hasChanges;

  const AdminHeader({
    super.key,
    required this.title,
    required this.currentSection,
    this.hasChanges = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: title,
                  fontSize: 28.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                SizedBox(height: 5.h),
                Row(
                  children: [
                    CustomText(
                      text: AppStrings.dashboard,
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                    CustomIcon(
                      icon: Icons.chevron_right,
                      size: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                    CustomText(
                      text: currentSection,
                      fontSize: 12.sp,
                      color: AppColors.primary,
                    ),
                  ],
                ),
              ],
            ),
            if (hasChanges)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: AppColors.warningGradient),
                  borderRadius: BorderRadius.circular(20.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.warning.withAlpha(30),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomIcon(
                      icon: Icons.warning,
                      size: 16.sp,
                      color: AppColors.textPrimary,
                    ),
                    SizedBox(width: 6.w),
                    CustomText(
                      text: AppStrings.unsavedChanges,
                      fontSize: 12.sp,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ],
    );
  }
}
