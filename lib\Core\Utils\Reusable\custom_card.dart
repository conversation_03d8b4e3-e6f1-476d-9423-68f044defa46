import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:flutter/material.dart';

class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final double? elevation;
  final Color? backgroundColor;

  const CustomCard({
    super.key,
    required this.child,
    this.padding,
    this.elevation,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: elevation ?? 8,
      color: backgroundColor ?? const Color(0xFF16213E),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          ResponsiveLayout.getBorderRadius(context),
        ),
      ),
      child: Padding(
        padding: padding ?? ResponsiveLayout.getContainerPadding(context),
        child: child,
      ),
    );
  }
}
