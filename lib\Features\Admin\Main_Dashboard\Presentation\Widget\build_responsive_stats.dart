import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../config/cubit/admin_cubit.dart';
import 'build_stat_card.dart';

class BuildResponsiveStats extends StatelessWidget {
  const BuildResponsiveStats({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AdminCubit>();
    var skillsCount = cubit.state.userData?.skills?.length ?? 0;
    var projectsCount = cubit.state.userData?.projects?.length ?? 0;
    var experienceCount = cubit.state.userData?.education?.length ?? 0;

    return  ResponsiveLayout.isMobile(context) ? Column(
      spacing: 10.h,
        children: [
          BuildStatCard(icon: Icons.psychology, count: '$skillsCount Skills', title: AppStrings.skills),
          BuildStatCard(icon: Icons.folder, count: '$projectsCount Projects', title: AppStrings.projects),
          BuildStatCard(icon: Icons.work, count: '$experienceCount Experience', title: AppStrings.experience),
        ],
      ) : Row(
        spacing: 15.w,
        children: [
          Expanded(
            child: BuildStatCard(icon: Icons.psychology, count: '$skillsCount Skills', title: AppStrings.skills),
          ),
          Expanded(
            child: BuildStatCard(icon: Icons.folder, count: '$projectsCount Projects', title: AppStrings.projects),
          ),
          Expanded(
            child: BuildStatCard(icon: Icons.work, count: '$experienceCount Experience', title: AppStrings.experience),
          ),
        ],
      );
  }
}
