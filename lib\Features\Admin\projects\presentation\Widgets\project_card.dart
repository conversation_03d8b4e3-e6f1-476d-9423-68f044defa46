import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/project_model.dart';

class ProjectCard extends StatelessWidget {
  final ProjectModel project;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const ProjectCard({
    super.key,
    required this.project,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(color: Colors.grey.withAlpha(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Project header
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF6366F1).withAlpha(10),
                  const Color(0xFF8B5CF6).withAlpha(10),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15.r),
                topRight: Radius.circular(15.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6366F1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.code,
                    color: Colors.white,
                    size: ResponsiveLayout.getIconSize(context),
                  ),
                ),
                SizedBox(width: 15.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: project.title ?? '',
                        fontSize: ResponsiveLayout.getBodyFontSize(context),
                        fontWeight: FontWeight.bold,
                      ),
                      CustomText(
                        text: project.category ?? '',
                        fontSize: ResponsiveLayout.getSmallFontSize(context),
                        color: const Color(0xFF6366F1),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.edit, color: Colors.blue, size: 20.sp),
                      onPressed: onEdit,
                      tooltip: 'Edit Project',
                    ),
                    IconButton(
                      icon: Icon(Icons.delete, color: Colors.red, size: 20.sp),
                      onPressed: onDelete,
                      tooltip: 'Delete Project',
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Project content
          Container(
            padding: EdgeInsets.all(20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: project.description ?? '',
                  fontSize: ResponsiveLayout.getBodyFontSize(context),
                  color: Colors.grey,
                  maxLines: 3,
                ),
                SizedBox(height: 15.h),
                // Technologies
                CustomText(
                  text: 'Technologies:',
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  fontWeight: FontWeight.w600,
                  color: Colors.grey,
                ),
                SizedBox(height: 8.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: (project.technologies ?? [])
                      .map(
                        (tech) => Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 6.h,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF6366F1).withAlpha(20),
                            borderRadius: BorderRadius.circular(20.r),
                            border: Border.all(
                              color: const Color(0xFF6366F1).withAlpha(30),
                            ),
                          ),
                          child: CustomText(
                            text: tech,
                            fontSize: ResponsiveLayout.getSmallFontSize(
                              context,
                            ),
                            color: const Color(0xFF6366F1),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                      .toList(),
                ),
                SizedBox(height: 15.h),
                // Links
                Row(
                  children: [
                    Expanded(
                      child: _buildLinkButton(
                        'GitHub',
                        Icons.link,
                        project.github ?? '',
                        Colors.grey,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Expanded(
                      child: _buildLinkButton(
                        'Live Demo',
                        Icons.open_in_new,
                        project.liveUrl ?? '',
                        Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinkButton(String text, IconData icon, String url, Color color) {
    return Builder(
      builder: (context) => Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        decoration: BoxDecoration(
          color: color.withAlpha(10),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: color.withAlpha(30)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 16.sp),
            SizedBox(width: 6.w),
            CustomText(
              text: text,
              fontSize: ResponsiveLayout.getSmallFontSize(context),
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ],
        ),
      ),
    );
  }
}
