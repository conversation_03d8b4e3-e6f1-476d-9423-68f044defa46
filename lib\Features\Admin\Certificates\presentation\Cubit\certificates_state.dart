import '../../../../../Core/models/certificate_model.dart';

class CertificatesState {
  final List<CertificateModel> certificatesList;
  final CertificateModel? editingCertificate;
  final bool isAddingNew;
  final bool isLoading;
  final String? errorMessage;
  final bool hasChanges;
  final bool? isSuccess;

  CertificatesState({
    this.certificatesList = const [],
    this.editingCertificate,
    this.isAddingNew = false,
    this.isLoading = false,
    this.errorMessage,
    this.hasChanges = false,
    this.isSuccess,
  });

  CertificatesState copyWith({
    List<CertificateModel>? certificatesList,
    CertificateModel? editingCertificate,
    bool? isAddingNew,
    bool? isLoading,
    String? errorMessage,
    bool? hasChanges,
    bool? isSuccess,
  }) {
    return CertificatesState(
      certificatesList: certificatesList ?? this.certificatesList,
      editingCertificate: editingCertificate ?? this.editingCertificate,
      isAddingNew: isAddingNew ?? this.isAddingNew,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      hasChanges: hasChanges ?? this.hasChanges,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
}
