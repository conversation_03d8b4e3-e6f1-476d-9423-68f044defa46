

import '../../../../../Core/Utils/Reusable/custom_button.dart';
import '../../../../../Core/Utils/Reusable/custom_text.dart';
import '../../../../../Core/Utils/widgets/build_dropdown_field.dart';
import '../../../../../Core/Utils/widgets/icon_selector.dart';
import '../cubit/skills_cubit.dart';
import '../cubit/skills_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Utils/widgets/build_text_field.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/skill_model.dart';
import '../../../../../Core/resources/resources.dart';

class SkillForm extends StatefulWidget {
  final SkillModel? skill;
  final bool isAddingNew;
  final Function(String name, String category, String icon) onSave;
  final VoidCallback onCancel;

  const SkillForm({
    super.key,
    this.skill,
    required this.isAddingNew,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<SkillForm> createState() => _SkillFormState();
}

class _SkillFormState extends State<SkillForm> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController nameController = TextEditingController();
  TextEditingController categoryController = TextEditingController();
  TextEditingController selectedIcon = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocListener<SkillsCubit, SkillsState>(
      listener: (context, state) {
        if (state.onIconSelected != null && state.onIconSelected != "") {
          selectedIcon.text = state.onIconSelected!;
          
        } 
        
         if (state.isEditing == true ) {
          nameController.text = state.editingSkill?.name ?? '';
          categoryController.text = state.editingSkill?.category ?? '';
          selectedIcon.text = state.editingSkill?.icon ?? "";
        } 
        
       else  if (state.isAddingNew == true && state.onIconSelected == "") {
          nameController.clear();
          categoryController.clear();
          selectedIcon.clear();

        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(15.w),
              decoration: BoxDecoration(
                color: AppColors.withOpacity(AppColors.primary, 0.05),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.isAddingNew ? Icons.add : Icons.edit,
                    color: AppColors.primary,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.w),
                  CustomText(
                    text: widget.isAddingNew
                        ? AppStrings.addNewSkill
                        : AppStrings.editSkill,
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                reverse: true,
                physics: const RangeMaintainingScrollPhysics(),
                padding: EdgeInsets.all(15.w),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      BuildTextField(
                        controller: nameController,
                        label: AppStrings.skillName,
                        icon: Icons.psychology,
                        hint: AppStrings.enterSkillName,
                      ),
                      SizedBox(height: 15.h),
                      BuildDropdownField(
                        onChanged: (value) {
                          categoryController.text = value ?? '';
                        },
                        controller: categoryController,
                        label: AppStrings.category,
                        icon: Icons.category,
                        items: AppList.categories,
                        hint: AppStrings.selectCategory,
                      ),
                      SizedBox(height: 15.h),
                      IconSelector(selectedIcon: selectedIcon.text),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.all(15.w),
              child: Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      text: widget.isAddingNew
                          ? AppStrings.addSkill
                          : AppStrings.updateSkill,
                      onPressed: _saveSkill,
                      backgroundColor: AppColors.primary,
                    ),
                  ),

                  SizedBox(width: 10.w),
                  Expanded(
                    child: CustomButton(
                      text: AppStrings.cancel,
                      onPressed: widget.onCancel,
                      backgroundColor: AppColors.transparent,
                      textColor: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveSkill() {
    if (_formKey.currentState!.validate()) {
      widget.onSave(
        nameController.text,
        categoryController.text,
        context.read<SkillsCubit>().getIcon(),
      );
    }
  }
}
