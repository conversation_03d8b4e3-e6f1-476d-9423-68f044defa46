// lib/user_data_service/_user_data_list_extensions.dart
import 'user_data_base_service.dart'; 

mixin UserDataListExtensions {


  static List<Map<String, dynamic>> _getListData(String key) {
    final userData = UserDataBaseService.getUserData();
    final rawList = userData?[key] ?? [];
    return (rawList as List)
        .map((item) => Map<String, dynamic>.from(item as Map))
        .toList();
  }

  static Future<void> _updateListData(
      String key, List<Map<String, dynamic>> list) async {
    await UserDataBaseService.updateUserData({key: list});
  }


  static Future<void> _addItemToList(
      String key, Map<String, dynamic> newItem) async {
    final currentList = _getListData(key);

    if (newItem['id'] == null) {
      final newId = currentList.isEmpty
          ? 1
          : currentList
                .map((item) => int.tryParse(item['id'].toString()) ?? 0)
                .reduce((a, b) => a > b ? a : b) +
              1;
      newItem['id'] = newId.toString();
    }

    currentList.add(newItem);
    await _updateListData(key, currentList);
  }

  static Future<void> _updateListItem(
      String key, String itemId, Map<String, dynamic> updatedItem) async {
    final currentList = _getListData(key);
    final index = currentList.indexWhere((item) => item['id'] == itemId);

    if (index != -1) {
      updatedItem['id'] = itemId;
      currentList[index] = updatedItem;
      await _updateListData(key, currentList);
    } else {
    }
  }

  static Future<void> _deleteListItem(String key, String itemId) async {
    final currentList = _getListData(key);
    final initialLength = currentList.length;
    currentList.removeWhere((item) => item['id'] == itemId);

    if (currentList.length < initialLength) {
      await _updateListData(key, currentList);
    } else {
    }
  }

  static Map<String, dynamic>? _getMapField(String key) {
    final userData = UserDataBaseService.getUserData();
    final field = userData?[key];
    return field != null
        ? Map<String, dynamic>.from(field as Map)
        : null;
  }


  static UserListManager list(String key) => UserListManager._(key);

  static Map<String, dynamic>? getMapField(String key) => _getMapField(key);

  static Future<void> updateMapField(
    String key,
    Map<String, dynamic> data,
  ) async {
    await UserDataBaseService.updateUserData({key: data});
  }
}

class UserListManager {
  final String _key;

  UserListManager._(this._key);

  List<Map<String, dynamic>> get() => UserDataListExtensions._getListData(_key);

  Future<void> updateAll(List<Map<String, dynamic>> list) async {
    await UserDataListExtensions._updateListData(_key, list); 
  }

  Future<void> add(Map<String, dynamic> newItem) async {
    await UserDataListExtensions._addItemToList(_key, newItem);
  }

  Future<void> update(String itemId, Map<String, dynamic> updatedItem) async {
    await UserDataListExtensions._updateListItem(_key, itemId, updatedItem);
  }

  Future<void> delete(String itemId) async {
    await UserDataListExtensions._deleteListItem(_key, itemId);
  }
}