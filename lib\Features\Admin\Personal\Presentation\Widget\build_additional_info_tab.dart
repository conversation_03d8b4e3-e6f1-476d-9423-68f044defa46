import 'package:devfolio/Core/resources/resources.dart';
import 'package:devfolio/Core/Utils/widgets/build_text_field.dart';
import 'package:devfolio/Features/Admin/Personal/Presentation/Widget/build_section_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildAdditionalInfoTab extends StatelessWidget {
  const BuildAdditionalInfoTab({
    super.key,
    required this.dateOfBirthController,
    required this.nationalityController,
    required this.languagesController,
    required this.interestsController,
  });
  final TextEditingController dateOfBirthController;
  final TextEditingController nationalityController;
  final TextEditingController languagesController;
  final TextEditingController interestsController;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BuildSectionTitle(title: AppStrings.additionalInformation),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: dateOfBirthController,
            label: AppStrings.dateOfBirth,
            icon: Icons.cake,
            keyboardType: TextInputType.datetime,
            hint: 'YYYY-MM-DD',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: nationalityController,
            label: AppStrings.nationality,
            icon: Icons.flag,
            hint: 'Your nationality',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: languagesController,
            label: AppStrings.languages,
            icon: Icons.language,
            maxLines: 2,
            hint: 'Languages you speak (e.g., Arabic, English, French)',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: interestsController,
            label: AppStrings.interestsHobbies,
            icon: Icons.favorite,
            maxLines: 3,
            hint: 'Your interests, hobbies, and activities...',
          ),
        ],
      ),
    );
  }
}
