class AdditionInfoModel {
  final String id;  
  final DateTime birthDate;  
  final String nationality;  
  final String language;  

  AdditionInfoModel({
    required this.id,
    required this.birthDate,
    required this.nationality,
    required this.language,
  });

  factory AdditionInfoModel.fromJson(Map<String, dynamic> json) {
    return AdditionInfoModel(
      id: json['id'],
      birthDate: json['dateTime'],
      nationality: json['nationality'],
      language: json['language'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dateTime': birthDate,
      'nationality': nationality,
      'language': language,
    };
  }
  static AdditionInfoModel empty() {
    return AdditionInfoModel(
      id: '-1',
      birthDate: DateTime.now(),
      nationality: 'Unknown',
      language: 'Unknown',
    );
  }
}