import 'package:devfolio/Features/Admin/Personal/Presentation/Widget/build_header_personal.dart';
import 'package:devfolio/Features/Admin/Personal/Presentation/cubit/personal_contact_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../Core/resources/resources.dart';
import 'cubit/personal_contact_cubit.dart';
import 'Widget/personal_tab_bar.dart';
import 'Widget/personal_tab_content.dart';
import 'Widget/personal_action_buttons.dart';

class PersonalContactEditor extends StatefulWidget {
  const PersonalContactEditor({super.key});

  @override
  State<PersonalContactEditor> createState() => _PersonalContactEditorState();
}

class _PersonalContactEditorState extends State<PersonalContactEditor> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PersonalContactCubit>().initPersonalContactData();
    });
  }
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PersonalContactCubit, PersonalContactState>(
      listener: (context, state) {},
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.all(25.w),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(15.r),
            border: Border.all(
              color: AppColors.withOpacity(AppColors.textSecondary, 0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.withOpacity(AppColors.shadowDark, 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BuildHeaderPersonal(),
              SizedBox(height: 25.h),
              PersonalTabBar(
                state: state,
                onTabChanged: (index) =>
                    context.read<PersonalContactCubit>().setCurrentTab(index),
              ),
              SizedBox(height: 20.h),
              Expanded(
                child: PersonalTabContent(
                  state: state,
                  onDataUpdate: (key, value) {
                    context.read<PersonalContactCubit>().updateField(
                      key,
                      value,
                    );
                   
                  },
                ),
              ),
              SizedBox(height: 20.h),
              state.hasChanges
                  ? PersonalActionButtons(
                      isLoading: state.isLoading,
                      onSave: () => _saveChanges(context),
                      onReset: () => _resetForm(context),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
        ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.1);
      },
    );
  }

  void _saveChanges(BuildContext context) {
    final cubit = context.read<PersonalContactCubit>();
    cubit.savePersonalContactData();
  }

  void _resetForm(BuildContext context) {
    final cubit = context.read<PersonalContactCubit>();
    cubit.reset();
  }
}
