import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../Core/layout/responsive_layout.dart';
import '../../config/cubit/portfolio_cubit.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      child: Center(
        child: SingleChildScrollView(
          child: BlocBuilder<PortfolioCubit, PortfolioState>(
            builder: (context, state) {
              final portfolioData = state.portfolioData;

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Profile Image
                  CircleAvatar(
                    radius: ResponsiveLayout.getAvatarSize(context) / 2,
                    backgroundColor: const Color(0xFF6366F1),
                    backgroundImage:
                        portfolioData?.personalInfo?.avatar != null &&
                            portfolioData!.personalInfo!.avatar!.isNotEmpty
                        ? NetworkImage(portfolioData.personalInfo!.avatar!)
                        : null,
                    child:
                        portfolioData?.personalInfo?.avatar == null ||
                            portfolioData!.personalInfo!.avatar!.isEmpty
                        ? Icon(
                            Icons.person,
                            size: ResponsiveLayout.getLargeIconSize(context),
                            color: Colors.white,
                          )
                        : null,
                  ),
                  SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

                  // Name
                  CustomText(
                    text:
                        portfolioData?.personalInfo?.name ?? 'Portfolio Owner',
                    fontSize: ResponsiveLayout.getTitleFontSize(context),
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),

                  // Title
                  CustomText(
                    text: portfolioData?.personalInfo?.title ?? 'Developer',
                    fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                    color: const Color(0xFF6366F1),
                    fontWeight: FontWeight.w600,
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

                  // Description
                  CustomText(
                    text:
                        portfolioData?.personalInfo?.description ??
                        'Passionate about creating beautiful and functional applications.',
                    fontSize: ResponsiveLayout.getBodyFontSize(context),
                    color: Colors.grey,
                    textAlign: TextAlign.center,
                    maxLines: 4,
                  ),

                  SizedBox(height: ResponsiveLayout.getLargeSpacing(context)),

                  // Action Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomButton(
                        text: 'View Projects',
                        onPressed: () {
                          // Navigate to projects section
                        },
                      ),
                      SizedBox(
                        width: ResponsiveLayout.getMediumSpacing(context),
                      ),
                      CustomButton(
                        text: 'Contact Me',
                        onPressed: () {
                          // Navigate to contact section
                        },
                        backgroundColor: Colors.transparent,
                        textColor: const Color(0xFF6366F1),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
