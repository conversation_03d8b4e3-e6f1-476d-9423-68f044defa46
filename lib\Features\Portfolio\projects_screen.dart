import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/project_card.dart';
import 'package:devfolio/Core/Utils/Reusable/section_container.dart';
import 'package:flutter/material.dart';
import '../../Core/layout/responsive_layout.dart';

class ProjectsScreen extends StatelessWidget {
  const ProjectsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'Projects',
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Featured Project
            CustomText(
              text: 'Featured Project',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            ProjectCard(
              title: 'E-Commerce Mobile App',
              description:
                  'A full-featured e-commerce application with user authentication, product catalog, shopping cart, and payment integration.',
              imageUrl:
                  'https://via.placeholder.com/400x200/6366F1/FFFFFF?text=E-Commerce+App',
              technologies: ['Flutter', 'Firebase', 'Stripe', 'BLoC'],
              onTap: () {
                // Navigate to project details
              },
            ),

            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),

            // Other Projects
            CustomText(
              text: 'Other Projects',
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),

            SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),

            // Project Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: ResponsiveLayout.getGridColumns(context),
              crossAxisSpacing: ResponsiveLayout.getMediumSpacing(context),
              mainAxisSpacing: ResponsiveLayout.getMediumSpacing(context),
              childAspectRatio: 0.8,
              children: [
                ProjectCard(
                  title: 'Task Management App',
                  description:
                      'A productivity app for managing tasks and projects with team collaboration features.',
                  technologies: ['Flutter', 'SQLite', 'Provider'],
                  onTap: () {},
                ),
                ProjectCard(
                  title: 'Weather App',
                  description:
                      'Real-time weather application with location-based forecasts and beautiful UI.',
                  technologies: ['Flutter', 'OpenWeather API', 'Geolocation'],
                  onTap: () {},
                ),
                ProjectCard(
                  title: 'Social Media App',
                  description:
                      'A social networking app with user profiles, posts, and real-time messaging.',
                  technologies: ['Flutter', 'Firebase', 'WebRTC'],
                  onTap: () {},
                ),
                ProjectCard(
                  title: 'Fitness Tracker',
                  description:
                      'Health and fitness tracking app with workout plans and progress monitoring.',
                  technologies: ['Flutter', 'Health APIs', 'Charts'],
                  onTap: () {},
                ),
                ProjectCard(
                  title: 'News Reader',
                  description:
                      'News aggregation app with multiple sources and personalized content.',
                  technologies: ['Flutter', 'REST APIs', 'Caching'],
                  onTap: () {},
                ),
                ProjectCard(
                  title: 'Budget Manager',
                  description:
                      'Personal finance app for tracking expenses and managing budgets.',
                  technologies: ['Flutter', 'SQLite', 'Charts'],
                  onTap: () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
