import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/education_model.dart';
import '../../../../../Core/resources/resources.dart';

class EducationCard extends StatelessWidget {
  final EducationModel education;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const EducationCard({
    super.key,
    required this.education,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with degree and actions
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CustomText(
                  text: education.degree ?? 'No Degree',
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: onEdit,
                    icon: Icon(
                      Icons.edit_outlined,
                        color: AppColors.primary,
                      size: 20.sp,
                    ),
                    tooltip: 'Edit',
                  ),
                  IconButton(
                    onPressed: onDelete,
                    icon: Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                      size: 20.sp,
                    ),
                    tooltip: 'Delete',
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 8.h),
          
          // Institution
          if (education.institution != null && education.institution!.isNotEmpty)
            Row(
              children: [
                Icon(
                  Icons.business_outlined,
                  color: Colors.grey,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CustomText(
                    text: education.institution!,
                    fontSize: ResponsiveLayout.getSmallFontSize(context),
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          
          // Location
          if (education.location != null && education.location!.isNotEmpty) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  color: Colors.grey,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: CustomText(
                    text: education.location!,
                    fontSize: ResponsiveLayout.getSmallFontSize(context),
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
          
          // Duration
          if (education.startDate != null || education.endDate != null) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  color: Colors.grey,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                CustomText(
                  text: _formatDuration(),
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  color: Colors.grey,
                ),
              ],
            ),
          ],
          
          // GPA
          if (education.gpa != null && education.gpa!.isNotEmpty) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(
                  Icons.grade_outlined,
                  color: Colors.grey,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                CustomText(
                  text: 'GPA: ${education.gpa}',
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  color: Colors.grey,
                ),
              ],
            ),
          ],
          
          // Description
          if (education.description != null && education.description!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            CustomText(
              text: education.description!,
              fontSize: ResponsiveLayout.getSmallFontSize(context),
              color: Colors.grey.withAlpha(200),
              maxLines: 3,
              
            ),
          ],
        ],
      ),
    );
  }

  String _formatDuration() {
    final start = education.startDate ?? '';
    final end = education.endDate ?? 'Present';
    
    if (start.isEmpty && end == 'Present') return '';
    if (start.isEmpty) return end;
    if (end == 'Present') return '$start - Present';
    
    return '$start - $end';
  }
}
