import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/education_model.dart';
import '../../../../../Core/resources/resources.dart';
import 'education_card.dart';

class EducationList extends StatelessWidget {
  final List<EducationModel> educationList;
  final Function(EducationModel) onEdit;
  final Function(EducationModel) onDelete;
  final bool isLoading;

  const EducationList({
    super.key,
    required this.educationList,
    required this.onEdit,
    required this.onDelete,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // List header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(
              text: 'Education Entries (${educationList.length})',
              fontSize: ResponsiveLayout.getSmallFontSize(context),
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
            if (isLoading)
              SizedBox(
                width: 20.w,
                height: 20.h,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppColors.primary,
                ),
              ),
          ],
        ),
        SizedBox(height: 15.h),
        
        // Education list
        Expanded(
          child: ListView.builder(
            itemCount: educationList.length,
            itemBuilder: (context, index) {
              final education = educationList[index];
              return Padding(
                padding: EdgeInsets.only(bottom: 15.h),
                child: EducationCard(
                  education: education,
                  onEdit: () => onEdit(education),
                  onDelete: () => onDelete(education),
                ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.1),
              );
            },
          ),
        ),
      ],
    );
  }
}
