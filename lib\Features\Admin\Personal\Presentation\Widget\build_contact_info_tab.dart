import 'package:devfolio/Core/resources/resources.dart';
import 'package:devfolio/Core/Utils/widgets/build_text_field.dart';
import 'package:devfolio/Features/Admin/Personal/Presentation/Widget/build_section_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildContactInfoTab extends StatelessWidget {
  const BuildContactInfoTab({
    super.key,
    required  this.emailController,
    required  this.phoneController,
    required  this.locationController,
    required  this.websiteController,
    required  this.linkedinController,
    required  this.githubController,
  }) ;

  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController locationController;
  final TextEditingController websiteController;
  final TextEditingController linkedinController;
  final TextEditingController githubController;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BuildSectionTitle(title:AppStrings.contactDetails),
          SizedB<PERSON>(height: 15.h),
          BuildTextField(
            controller: emailController,
            label: AppStrings.emailAddress,
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            hint: AppStrings.emailExample,
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: phoneController,
            label: AppStrings.phoneNumber,
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
            hint: AppStrings.phoneExample,
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: locationController,
            label: AppStrings.location,
            icon: Icons.location_on,
            hint: AppStrings.locationExample,
          ),
          SizedBox(height: 20.h),
          BuildSectionTitle(title:AppStrings.professionalLinks),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: websiteController,
            label: AppStrings.website,
            icon: Icons.web,
            keyboardType: TextInputType.url,
            hint: 'https://yourwebsite.com',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: linkedinController,
            label: AppStrings.linkedin,
            icon: Icons.link,
            keyboardType: TextInputType.url,
            hint: 'https://linkedin.com/in/yourprofile',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: githubController,
            label: AppStrings.github,
            icon: Icons.code,
            keyboardType: TextInputType.url,
            hint: 'https://github.com/yourusername',
          ),
        ],
      ),
    );
  }
}
