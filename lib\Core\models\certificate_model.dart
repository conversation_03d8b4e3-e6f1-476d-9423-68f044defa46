class CertificateModel {
  final String? id;
  final String? name;
  final String? issuingOrganization;
  final String? issueDate;
  final String? expiryDate;
  final String? credentialId;
  final String? description;

  CertificateModel({
    this.id,
    this.name,
    this.issuingOrganization,
    this.issueDate,
    this.expiryDate,
    this.credentialId,
    this.description,
  });

  factory CertificateModel.fromJson(Map<String, dynamic> json) {
    return CertificateModel(
      id: json['id'],
      name: json['name'],
      issuingOrganization: json['issuingOrganization'] ?? json['issuing_organization'],
      issueDate: json['issueDate'] ?? json['issue_date'],
      expiryDate: json['expiryDate'] ?? json['expiry_date'],
      credentialId: json['credentialId'] ?? json['credential_id'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (issuingOrganization != null) 'issuingOrganization': issuingOrganization,
      if (issueDate != null) 'issueDate': issueDate,
      if (expiryDate != null) 'expiryDate': expiryDate,
      if (credentialId != null) 'credentialId': credentialId,
      if (description != null) 'description': description,
    };
  }

  CertificateModel copyWith({
    String? id,
    String? name,
    String? issuingOrganization,
    String? issueDate,
    String? expiryDate,
    String? credentialId,
    String? description,
  }) {
    return CertificateModel(
      id: id ?? this.id,
      name: name ?? this.name,
      issuingOrganization: issuingOrganization ?? this.issuingOrganization,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      credentialId: credentialId ?? this.credentialId,
      description: description ?? this.description,
    );
  }

  static String generateTimeIdString() {
    return (DateTime.now().millisecondsSinceEpoch % 1000000).toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CertificateModel &&
        other.id == id &&
        other.name == name &&
        other.issuingOrganization == issuingOrganization &&
        other.issueDate == issueDate &&
        other.expiryDate == expiryDate &&
        other.credentialId == credentialId &&
        other.description == description;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        issuingOrganization.hashCode ^
        issueDate.hashCode ^
        expiryDate.hashCode ^
        credentialId.hashCode ^
        description.hashCode;
  }

  @override
  String toString() {
    return 'CertificateModel(id: $id, name: $name, issuingOrganization: $issuingOrganization, issueDate: $issueDate, expiryDate: $expiryDate, credentialId: $credentialId, description: $description)';
  }
}
