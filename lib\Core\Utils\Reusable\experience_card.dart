import 'package:devfolio/Core/Utils/Reusable/custom_card.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_icon.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:flutter/material.dart';

class ExperienceCard extends StatelessWidget {
  final String company;
  final String position;
  final String duration;
  final String description;
  final List<String> achievements;

  const ExperienceCard({
    super.key,
    required this.company,
    required this.position,
    required this.duration,
    required this.description,
    required this.achievements,
  });

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CustomText(
                  text: company,
                  fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                  fontWeight: FontWeight.bold,
                ),
              ),
              CustomText(
                text: duration,
                fontSize: ResponsiveLayout.getSmallFontSize(context),
                color: Colors.grey,
              ),
            ],
          ),
          SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
          CustomText(
            text: position,
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            color: const Color(0xFF6366F1),
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),
          CustomText(
            text: description,
            fontSize: ResponsiveLayout.getSmallFontSize(context),
            color: Colors.grey,
          ),
          if (achievements.isNotEmpty) ...[
            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),
            ...achievements.map(
              (achievement) => Padding(
                padding: EdgeInsets.only(
                  bottom: ResponsiveLayout.getSmallSpacing(context),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomIcon(
                      icon: Icons.check_circle,
                      size: ResponsiveLayout.getIconSize(context),
                      color: const Color(0xFF6366F1),
                    ),
                    SizedBox(width: ResponsiveLayout.getSmallSpacing(context)),
                    Expanded(
                      child: CustomText(
                        text: achievement,
                        fontSize: ResponsiveLayout.getSmallFontSize(context),
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
