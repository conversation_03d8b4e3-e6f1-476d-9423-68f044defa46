import 'package:dartz/dartz.dart';
import '../../../Core/models/personal_info_model.dart';
import '../../../Core/models/skill_model.dart';
import '../../../Core/models/education_model.dart';
import '../../../Core/models/certificate_model.dart';
import '../../../Core/models/experience_model.dart';
import '../../../Core/models/project_model.dart';
import '../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../../Core/Storage/Local/UserDataService/user_data_base_service.dart';
import '../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../Core/services/Subabase/subabase_services.dart';
import '../../../Core/services/Subabase/class_tables.dart';
import '../../Admin/Personal/Data/personal_source.dart';
import '../../Admin/Skills/Data/skills_source.dart';
import '../../Admin/Education/Data/education_source.dart';
import '../../Admin/Certificates/Data/certificates_source.dart';
import '../../Admin/Experience/Data/experience_source.dart';
import '../../Admin/projects/Data/projects_source.dart';

/// Result wrapper for individual portfolio sections
class SectionResult<T> {
  final bool isLoading;
  final bool hasError;
  final String? errorMessage;
  final T? data;

  SectionResult({
    required this.isLoading,
    required this.hasError,
    this.errorMessage,
    this.data,
  });

  bool get hasData => data != null && !hasError;
  bool get isEmpty => data == null || (data is List && (data as List).isEmpty);
}

/// Comprehensive portfolio data container
class PortfolioSectionData {
  SectionResult<PersonalInfoModel>? personalInfo;
  SectionResult<List<SkillModel>>? skills;
  SectionResult<List<EducationModel>>? education;
  SectionResult<List<CertificateModel>>? certifications;
  SectionResult<List<ExperienceModel>>? experience;
  SectionResult<List<ProjectModel>>? projects;

  PortfolioSectionData({
    this.personalInfo,
    this.skills,
    this.education,
    this.certifications,
    this.experience,
    this.projects,
  });

  /// Get list of sections that have data
  List<String> get availableSections {
    final sections = <String>[];
    if (personalInfo?.hasData == true) sections.add('personal');
    if (skills?.hasData == true && skills?.data?.isNotEmpty == true)
      sections.add('skills');
    if (education?.hasData == true && education?.data?.isNotEmpty == true)
      sections.add('education');
    if (certifications?.hasData == true &&
        certifications?.data?.isNotEmpty == true)
      sections.add('certifications');
    if (experience?.hasData == true && experience?.data?.isNotEmpty == true)
      sections.add('experience');
    if (projects?.hasData == true && projects?.data?.isNotEmpty == true)
      sections.add('projects');
    return sections;
  }

  /// Check if any section is currently loading
  bool get isAnyLoading {
    return personalInfo?.isLoading == true ||
        skills?.isLoading == true ||
        education?.isLoading == true ||
        certifications?.isLoading == true ||
        experience?.isLoading == true ||
        projects?.isLoading == true;
  }

  /// Check if any section has errors
  bool get hasAnyErrors {
    return personalInfo?.hasError == true ||
        skills?.hasError == true ||
        education?.hasError == true ||
        certifications?.hasError == true ||
        experience?.hasError == true ||
        projects?.hasError == true;
  }
}

class PortfolioDataService {
  /// Comprehensive portfolio data model that includes all sections
  static Future<Either<String, PortfolioSectionData>> getAllPortfolioData({
    bool refresh = false,
    String? userId,
  }) async {
    try {
      final String userIdToUse = userId ?? UserDataBaseService.getUserDataId();

      if (userIdToUse.isEmpty) {
        return Left('User ID not found');
      }

      // Initialize section data
      final sectionData = PortfolioSectionData();

      // Fetch all sections concurrently for better performance
      final results = await Future.wait([
        _fetchPersonalInfo(refresh: refresh),
        _fetchSkills(refresh: refresh, userId: userIdToUse),
        _fetchEducation(refresh: refresh, userId: userIdToUse),
        _fetchCertifications(refresh: refresh, userId: userIdToUse),
        _fetchExperience(refresh: refresh, userId: userIdToUse),
        _fetchProjects(refresh: refresh, userId: userIdToUse),
      ]);

      // Process results and update section data
      sectionData.personalInfo = results[0] as SectionResult<PersonalInfoModel>;
      sectionData.skills = results[1] as SectionResult<List<SkillModel>>;
      sectionData.education = results[2] as SectionResult<List<EducationModel>>;
      sectionData.certifications =
          results[3] as SectionResult<List<CertificationModel>>;
      sectionData.experience =
          results[4] as SectionResult<List<ExperienceModel>>;
      sectionData.projects = results[5] as SectionResult<List<ProjectModel>>;

      return Right(sectionData);
    } catch (e) {
      return Left('Failed to fetch portfolio data: $e');
    }
  }

  /// Fetch personal information with error handling
  static Future<SectionResult<PersonalInfoModel>> _fetchPersonalInfo({
    bool refresh = false,
  }) async {
    try {
      final result = await PersonalSource.getPersonalInfo(refresh: refresh);
      return result.fold(
        (error) => SectionResult<PersonalInfoModel>(
          isLoading: false,
          hasError: true,
          errorMessage: error,
          data: null,
        ),
        (data) => SectionResult<PersonalInfoModel>(
          isLoading: false,
          hasError: false,
          errorMessage: null,
          data: data,
        ),
      );
    } catch (e) {
      return SectionResult<PersonalInfoModel>(
        isLoading: false,
        hasError: true,
        errorMessage: 'Failed to load personal info: $e',
        data: null,
      );
    }
  }

  /// Fetch skills with error handling
  static Future<SectionResult<List<SkillModel>>> _fetchSkills({
    bool refresh = false,
    required String userId,
  }) async {
    try {
      final result = await SkillsSource.getSkills(
        refresh: refresh,
        userId: userId,
      );
      return result.fold(
        (error) => SectionResult<List<SkillModel>>(
          isLoading: false,
          hasError: true,
          errorMessage: error,
          data: [],
        ),
        (data) => SectionResult<List<SkillModel>>(
          isLoading: false,
          hasError: false,
          errorMessage: null,
          data: data,
        ),
      );
    } catch (e) {
      return SectionResult<List<SkillModel>>(
        isLoading: false,
        hasError: true,
        errorMessage: 'Failed to load skills: $e',
        data: [],
      );
    }
  }

  /// Fetch education with error handling
  static Future<SectionResult<List<EducationModel>>> _fetchEducation({
    bool refresh = false,
    required String userId,
  }) async {
    try {
      final result = await EducationSource.getEducation(
        refresh: refresh,
        userId: userId,
      );
      return result.fold(
        (error) => SectionResult<List<EducationModel>>(
          isLoading: false,
          hasError: true,
          errorMessage: error,
          data: [],
        ),
        (data) => SectionResult<List<EducationModel>>(
          isLoading: false,
          hasError: false,
          errorMessage: null,
          data: data,
        ),
      );
    } catch (e) {
      return SectionResult<List<EducationModel>>(
        isLoading: false,
        hasError: true,
        errorMessage: 'Failed to load education: $e',
        data: [],
      );
    }
  }

  /// Fetch certifications with error handling
  static Future<SectionResult<List<CertificationModel>>> _fetchCertifications({
    bool refresh = false,
    required String userId,
  }) async {
    try {
      final result = await CertificatesSource.getCertificates(
        refresh: refresh,
        userId: userId,
      );
      return result.fold(
        (error) => SectionResult<List<CertificationModel>>(
          isLoading: false,
          hasError: true,
          errorMessage: error,
          data: [],
        ),
        (data) => SectionResult<List<CertificationModel>>(
          isLoading: false,
          hasError: false,
          errorMessage: null,
          data: data,
        ),
      );
    } catch (e) {
      return SectionResult<List<CertificationModel>>(
        isLoading: false,
        hasError: true,
        errorMessage: 'Failed to load certifications: $e',
        data: [],
      );
    }
  }

  /// Fetch experience with error handling
  static Future<SectionResult<List<ExperienceModel>>> _fetchExperience({
    bool refresh = false,
    required String userId,
  }) async {
    try {
      final result = await ExperienceSource.getExperience(
        refresh: refresh,
        userId: userId,
      );
      return result.fold(
        (error) => SectionResult<List<ExperienceModel>>(
          isLoading: false,
          hasError: true,
          errorMessage: error,
          data: [],
        ),
        (data) => SectionResult<List<ExperienceModel>>(
          isLoading: false,
          hasError: false,
          errorMessage: null,
          data: data,
        ),
      );
    } catch (e) {
      return SectionResult<List<ExperienceModel>>(
        isLoading: false,
        hasError: true,
        errorMessage: 'Failed to load experience: $e',
        data: [],
      );
    }
  }

  /// Fetch projects with error handling
  static Future<SectionResult<List<ProjectModel>>> _fetchProjects({
    bool refresh = false,
    required String userId,
  }) async {
    try {
      final result = await ProjectsSource.getProjects(
        refresh: refresh,
        userId: userId,
      );
      return result.fold(
        (error) => SectionResult<List<ProjectModel>>(
          isLoading: false,
          hasError: true,
          errorMessage: error,
          data: [],
        ),
        (data) => SectionResult<List<ProjectModel>>(
          isLoading: false,
          hasError: false,
          errorMessage: null,
          data: data,
        ),
      );
    } catch (e) {
      return SectionResult<List<ProjectModel>>(
        isLoading: false,
        hasError: true,
        errorMessage: 'Failed to load projects: $e',
        data: [],
      );
    }
  }

  /// Refresh specific section data
  static Future<Either<String, SectionResult<T>>> refreshSection<T>(
    String sectionName, {
    String? userId,
  }) async {
    try {
      final String userIdToUse = userId ?? UserDataBaseService.getUserDataId();

      switch (sectionName.toLowerCase()) {
        case 'personal':
          final result = await _fetchPersonalInfo(refresh: true);
          return Right(result as SectionResult<T>);
        case 'skills':
          final result = await _fetchSkills(refresh: true, userId: userIdToUse);
          return Right(result as SectionResult<T>);
        case 'education':
          final result = await _fetchEducation(
            refresh: true,
            userId: userIdToUse,
          );
          return Right(result as SectionResult<T>);
        case 'certifications':
          final result = await _fetchCertifications(
            refresh: true,
            userId: userIdToUse,
          );
          return Right(result as SectionResult<T>);
        case 'experience':
          final result = await _fetchExperience(
            refresh: true,
            userId: userIdToUse,
          );
          return Right(result as SectionResult<T>);
        case 'projects':
          final result = await _fetchProjects(
            refresh: true,
            userId: userIdToUse,
          );
          return Right(result as SectionResult<T>);
        default:
          return Left('Unknown section: $sectionName');
      }
    } catch (e) {
      return Left('Failed to refresh section $sectionName: $e');
    }
  }

  /// Get cached data for quick loading
  static PortfolioSectionData getCachedData() {
    final sectionData = PortfolioSectionData();

    try {
      // Get cached personal info
      final personalData = UserDataService.getMapField(
        LocalStorageKeys.personalInfo,
      );
      if (personalData != null) {
        sectionData.personalInfo = SectionResult<PersonalInfoModel>(
          isLoading: false,
          hasError: false,
          data: PersonalInfoModel.fromJson(personalData),
        );
      }

      // Get cached skills
      final skillsData = UserDataService.getListData(LocalStorageKeys.skills);
      if (skillsData.isNotEmpty) {
        sectionData.skills = SectionResult<List<SkillModel>>(
          isLoading: false,
          hasError: false,
          data: skillsData.map((e) => SkillModel.fromJson(e)).toList(),
        );
      }

      // Get cached education
      final educationData = UserDataService.getListData(
        LocalStorageKeys.education,
      );
      if (educationData.isNotEmpty) {
        sectionData.education = SectionResult<List<EducationModel>>(
          isLoading: false,
          hasError: false,
          data: educationData.map((e) => EducationModel.fromJson(e)).toList(),
        );
      }

      // Get cached certifications
      final certificatesData = UserDataService.getListData(
        LocalStorageKeys.certificates,
      );
      if (certificatesData.isNotEmpty) {
        sectionData.certifications = SectionResult<List<CertificationModel>>(
          isLoading: false,
          hasError: false,
          data: certificatesData
              .map((e) => CertificationModel.fromJson(e))
              .toList(),
        );
      }

      // Get cached experience
      final experienceData = UserDataService.getListData(
        LocalStorageKeys.experience,
      );
      if (experienceData.isNotEmpty) {
        sectionData.experience = SectionResult<List<ExperienceModel>>(
          isLoading: false,
          hasError: false,
          data: experienceData.map((e) => ExperienceModel.fromJson(e)).toList(),
        );
      }

      // Get cached projects
      final projectsData = UserDataService.getListData(
        LocalStorageKeys.projects,
      );
      if (projectsData.isNotEmpty) {
        sectionData.projects = SectionResult<List<ProjectModel>>(
          isLoading: false,
          hasError: false,
          data: projectsData.map((e) => ProjectModel.fromJson(e)).toList(),
        );
      }
    } catch (e) {
      // If there's an error reading cache, return empty data
    }

    return sectionData;
  }
}
