import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:dartz/dartz.dart';
import '../../../../Core/services/Subabase/subabase_services.dart';
import '../../../../Core/resources/app_constants.dart';

class ProjectImageService {
  static const String _bucketName = 'project-images';
  
  /// Pick an image file from the device
  static Future<Either<String, Uint8List>> pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        return Left('No image selected');
      }

      final file = result.files.first;
      
      // Validate file size
      if (file.size > AppConstants.maxImageSize) {
        return Left('Image size must be less than ${AppConstants.maxImageSize ~/ (1024 * 1024)}MB');
      }

      // Validate file type
      if (file.extension == null || !_isValidImageExtension(file.extension!)) {
        return Left('Please select a valid image file (jpg, jpeg, png, gif, webp)');
      }

      if (file.bytes == null) {
        return Left('Failed to read image data');
      }

      return Right(file.bytes!);
    } catch (e) {
      return Left('Failed to pick image: $e');
    }
  }

  /// Upload image to Supabase storage
  static Future<Either<String, String>> uploadImage({
    required Uint8List imageBytes,
    required String projectId,
    String? fileName,
  }) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final imagePath = 'projects/$projectId/${fileName ?? 'image_$timestamp'}.jpg';
      
      final response = await SubabaseServices.uploadImage(
        bucket: _bucketName,
        path: imagePath,
        fileBytes: imageBytes,
        fileType: 'image/jpeg',
      );

      if (response.status) {
        return Right(response.data as String);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left('Failed to upload image: $e');
    }
  }

  /// Delete image from Supabase storage
  static Future<Either<String, bool>> deleteImage(String imageUrl) async {
    try {
      // Extract path from URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      
      if (pathSegments.length < 3) {
        return Left('Invalid image URL format');
      }
      
      // Remove the bucket name and storage prefix to get the actual path
      final imagePath = pathSegments.skip(2).join('/');
      
      final response = await SubabaseServices.deleteImage(
        bucket: _bucketName,
        path: imagePath,
      );

      if (response.status) {
        return Right(true);
      } else {
        return Left(response.message);
      }
    } catch (e) {
      return Left('Failed to delete image: $e');
    }
  }

  /// Validate image extension
  static bool _isValidImageExtension(String extension) {
    const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    return validExtensions.contains(extension.toLowerCase());
  }

  /// Get image file info for validation
  static Either<String, Map<String, dynamic>> getImageInfo(PlatformFile file) {
    try {
      return Right({
        'name': file.name,
        'size': file.size,
        'extension': file.extension,
        'sizeInMB': (file.size / (1024 * 1024)).toStringAsFixed(2),
        'isValid': file.size <= AppConstants.maxImageSize && 
                   file.extension != null && 
                   _isValidImageExtension(file.extension!),
      });
    } catch (e) {
      return Left('Failed to get image info: $e');
    }
  }
}
