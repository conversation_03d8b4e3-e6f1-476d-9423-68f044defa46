import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:flutter/material.dart';

class SkillChip extends StatelessWidget {
  final String skill;
  final Color? backgroundColor;
  final Color? textColor;

  const SkillChip({
    super.key,
    required this.skill,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: ResponsiveLayout.getSmallButtonPadding(context),
      decoration: BoxDecoration(
        color: backgroundColor ?? const Color(0xFF6366F1),
        borderRadius: BorderRadius.circular(
          ResponsiveLayout.getBorderRadius(context),
        ),
      ),
      child: CustomText(
        text: skill,
        fontSize: ResponsiveLayout.getSmallFontSize(context),
        color: textColor ?? Colors.white,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
