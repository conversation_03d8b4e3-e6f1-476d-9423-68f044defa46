import 'package:devfolio/Core/Utils/Reusable/custom_icon.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final String count;
  final Color? iconColor;
  final Color? countColor;
  final Color? titleColor;

  const StatCard({
    super.key,
    required this.title,
    required this.icon,
    required this.count,
    this.iconColor,
    this.countColor,
    this.titleColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        children: [
          CustomIcon(
            icon: icon,
            size: 24.sp,
            color: iconColor ?? AppColors.primary,
          ),
          SizedBox(height: 10.h),
          CustomText(
            text: count,
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: countColor ?? AppColors.primary,
          ),
          CustomText(
            text: title,
            fontSize: 12.sp,
            color: titleColor ?? AppColors.textSecondary,
          ),
        ],
      ),
    );
  }
}
