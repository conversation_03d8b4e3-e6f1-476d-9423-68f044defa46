String? validatePassword(
  bool singIn, {
  required String password,
  bool isRtl = false,
}) {
  if (password.isEmpty) {
    return isRtl ? "الرجاء إدخال كلمة المرور" : "Please Enter Password";
  }
  //Password must contain at least 6 characters
  else if (singIn) {
    if (password.length < 6) {
      return isRtl
          ? 'يجب أن تكون كلمة المرور على الأقل 6 محارف'
          : 'The password must be at least 6 characters long.';
    }
    // must contain at least one uppercase letter
    else if (!password.contains(RegExp(r'[A-Z]'))) {
      return isRtl
          ? 'يجب أن يحتوي كلمة المرور على الأقل حرف كبير'
          : 'The password must contain at least one uppercase letter.';
    }
    // Password must contain at least one lowercase letter
    else if (!password.contains(RegExp(r'[a-z]'))) {
      return isRtl
          ? 'يجب أن يحتوي كلمة المرور على الأقل حرف صغير'
          : 'The password must contain at least one lowercase letter.';
    }
    // Password must contain at least one digit
    else if (!password.contains(RegExp(r'[0-9]'))) {
      return isRtl
          ? 'يجب أن يحتوي كلمة المرور على الأقل رقم'
          : 'The password must contain at least one digit.';
    }
    // Password must not contain only numbers
    else if (RegExp(r'^[0-9]+$').hasMatch(password)) {
      return isRtl
          ? 'لا يجب أن تحتوي كلمة المرور فقط على أرقام'
          : 'The password must not contain only numbers.';
    }
  }
  return null;
}