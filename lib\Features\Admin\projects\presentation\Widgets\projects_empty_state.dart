import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';

class ProjectsEmptyState extends StatelessWidget {
  final VoidCallback onAddProject;

  const ProjectsEmptyState({super.key, required this.onAddProject});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(30.w),
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A2A),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(Icons.folder_open, size: 80.sp, color: Colors.grey),
          ),
          SizedBox(height: 20.h),
          CustomText(
            text: 'No Projects Yet',
            fontSize: ResponsiveLayout.getSubtitleFontSize(context),
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: 10.h),
          CustomText(
            text: 'Start by adding your first project to showcase your work',
            fontSize: ResponsiveLayout.getBodyFontSize(context),
            color: Colors.grey,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          CustomButton(
            text: 'Add Your First Project',
            onPressed: onAddProject,
            backgroundColor: const Color(0xFF6366F1),
          ),
        ],
      ),
    );
  }
}
