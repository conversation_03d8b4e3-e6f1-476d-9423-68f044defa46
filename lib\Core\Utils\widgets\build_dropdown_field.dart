
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildDropdownField extends StatelessWidget {
  const BuildDropdownField({
    super.key,
    required this.controller,
    required this.label,
    required this.icon,
    required this.items,
    required this.hint,
    required this.onChanged,
  });

  final TextEditingController controller;
  final String label;
  final IconData icon;
  final List<String> items;
  final String hint;
  final ValueChanged<String?>? onChanged;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 18.sp),
            SizedBox(width: 8.w),
            CustomText(
              text: label,
              fontSize: ResponsiveLayout.getBodyFontSize(context),
              fontWeight: FontWeight.w600,
            ),
          ],
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<String>(
          value: items.contains(controller.text) ? controller.text : null,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: AppColors.withOpacity(AppColors.textSecondary, 0.6),
              fontSize: ResponsiveLayout.getBodyFontSize(context),
            ),
            filled: true,
            fillColor: AppColors.cardSurface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: AppColors.withOpacity(AppColors.textSecondary, 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 10.h,
            ),
          ),
          items: items.map((String category) {
            return DropdownMenuItem<String>(
              value: category,
              child: Text(category),
            );
          }).toList(),
          onChanged: onChanged,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppStrings.requiredField;
            }
            return null;
          },
        ),
      ],
    );
  }
}
