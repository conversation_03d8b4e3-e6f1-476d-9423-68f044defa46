String? validateEmail({required String email, bool isRtl = false}) {
  const String pattern = r'^[\w-]+(\.[\w-]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$';
  final RegExp regex = RegExp(pattern);

  //------------------------
  if (email.isEmpty) {
    return isRtl ? "الرجاء ادخال البريد الالكتروني" : "Please Enter your Email";
  } else if (!regex.hasMatch(email)) {
    return isRtl ? "عنوان البريد الالكتروني غير صحيح" : 'The email address is badly formatted.';
  }
  return null;
}