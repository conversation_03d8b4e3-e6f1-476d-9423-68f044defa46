import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/Utils/widgets/build_popup_menu_item.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../config/cubit/admin_cubit.dart';

class BuildMobileHeaderMenueDashboard extends StatelessWidget {
  const BuildMobileHeaderMenueDashboard({
    super.key,
  });


  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.95),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
              ),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.admin_panel_settings,
              color: Colors.white,
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.adminPanel,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  AppStrings.portfolioManager,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // Mobile menu button
          PopupMenuButton<int>(
                icon: Icon(
                  Icons.more_vert,
                  color: AppColors.textPrimary,
                  size: 24.sp,
                ),
                onSelected: (section) {
                  if (section == -1) {
                    // Logout action
                    // _showLogoutDialog(context);
                  } else {
                    context.read<AdminCubit>().setSelectedSection(section);
                  }
                },
                itemBuilder: (context) => [
                  BuildPopupMenuItem(index: 0, icon: Icons.dashboard, label: AppStrings.dashboard),
                  BuildPopupMenuItem(index: 1, icon: Icons.person, label: AppStrings.personalInfo),
                  BuildPopupMenuItem(index: 2, icon: Icons.psychology, label: AppStrings.skills),
                  BuildPopupMenuItem(index: 3, icon: Icons.folder, label: AppStrings.projects),
                  BuildPopupMenuItem(index: 4, icon: Icons.school, label: AppStrings.education),
                  const PopupMenuDivider(),
                  BuildPopupMenuItem(index: -1, icon: Icons.logout, label: 'Logout'),
                ],
              )
        ],
      ),
    );
  }
}
