import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/widgets/build_loading.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final bool? isLoading;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? 250.w,
      height: height ?? 50.h,
      child: ElevatedButton(
        onPressed: isLoading == true ? () {} : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? const Color(0xFF6366F1),
          foregroundColor: textColor ?? Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              ResponsiveLayout.getBorderRadius(context),
            ),
          ),
        ),
        child: isLoading == true
            ? buildLoading()
            : CustomText(
                text: text,
                fontSize: ResponsiveLayout.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
              ),
      ),
    );
  }
}
