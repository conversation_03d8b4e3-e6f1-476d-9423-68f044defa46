import 'package:devfolio/Features/Admin/Main_Dashboard/Presentation/Widget/build_responsive_dashboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../Core/models/portfolio_data_model.dart';
import '../../Core/resources/app_constants.dart';

import '../../Features/Admin/Personal/Presentation/cubit/personal_contact_cubit.dart';
import '../../Features/Admin/Personal/Presentation/personal_contact_page.dart';
import '../../Features/Admin/Skills/Presentation/Pages/skills_page.dart';
import '../../Features/Admin/Skills/Presentation/cubit/skills_cubit.dart';
import '../../Features/Admin/projects/presentation/Cubit/projects_cubit.dart';
import '../../Features/Admin/projects/presentation/Pages/projects_editor.dart';
import '../../Features/Admin/Education/presentation/Cubit/education_cubit.dart';
import '../../Features/Admin/Education/presentation/Pages/education_editor.dart';
import '../../Features/Admin/Experience/presentation/Cubit/experience_cubit.dart';
import '../../Features/Admin/Experience/presentation/Pages/experience_editor.dart';
import '../../Features/Admin/Certificates/presentation/Cubit/certificates_cubit.dart';
import '../../Features/Admin/Certificates/presentation/Pages/certificates_editor.dart';

// Admin State
class AdminState {
  final int selectedSection;
  final bool hasChanges;
  final bool isLoading;
  final String currentSectionName;
  final PortfolioDataModel? userData;
  final bool isEditing;
  final String? errorMessage;

  AdminState({
    this.selectedSection = 0,
    this.hasChanges = false,
    this.isLoading = false,
    this.currentSectionName = 'Personal Info',
    this.userData,
    this.isEditing = false,
    this.errorMessage,
  });

  AdminState copyWith({
    int? selectedSection,
    bool? hasChanges,
    bool? isLoading,
    String? currentSectionName,
    PortfolioDataModel? userData,
    bool? isEditing,
    String? errorMessage,
  }) {
    return AdminState(
      selectedSection: selectedSection ?? this.selectedSection,
      hasChanges: hasChanges ?? this.hasChanges,
      isLoading: isLoading ?? this.isLoading,
      currentSectionName: currentSectionName ?? this.currentSectionName,
      userData: userData ?? this.userData,
      isEditing: isEditing ?? this.isEditing,
      errorMessage: errorMessage,
    );
  }
}

// Admin Cubit
class AdminCubit extends Cubit<AdminState> {
  AdminCubit() : super(AdminState());

  Future<void> init() async {
    emit(state.copyWith(isLoading: true));
    final userData = UserDataService.getUserData();
    if (userData != null) {
      final portfolioData = PortfolioDataModel.fromJson(userData);
      emit(state.copyWith(isLoading: false, userData: portfolioData));
    } else {
      emit(state.copyWith(isLoading: false, userData: null));
    }
  }

  // Section names for easy access
  static const List<String> sectionNames = [
    'Personal Info',
    'Skills',
    'Experience',
    'Projects',
    'Education',
    'Contact',
    'Certificates',
    
  ];

  // Set selected section
  void setSelectedSection(int section) {
    if (section >= 0 && section < sectionNames.length) {
      emit(
        state.copyWith(
          selectedSection: section,
          currentSectionName: sectionNames[section],
        ),
      );
    }
  }

  Widget buildContent() {
    // var projects = state.userData?.projects;
    // var education = state.userData?.education;
    final id = state.userData?.userId;

    switch (state.selectedSection) {
      case AppConstants.dashboardIndex:
        return BuildResponsiveDashboard();
      case AppConstants.personalInfoIndex:
        return BlocProvider(
          create: (context) => PersonalContactCubit(),
          child: PersonalContactEditor(),
        );
      case AppConstants.skillsIndex:
        return BlocProvider(
          create: (context) =>
              SkillsCubit()..initSkills(state.userData!.skills ?? [], id!),
          child: Skillspage(),
        );
      case AppConstants.projectsIndex:
        return BlocProvider(
          create: (context) => ProjectsCubit()..loadProjects(),
          child: ProjectsEditor(),
        );
      case AppConstants.educationIndex:
        return BlocProvider(
          create: (context) => EducationCubit()..loadEducation(),
          child: const EducationEditor(),
        );
      case AppConstants.experienceIndex:
        return BlocProvider(
          create: (context) => ExperienceCubit()..loadExperience(),
          child: const ExperienceEditor(),
        );
      case AppConstants.certificatesIndex:
        return BlocProvider(
          create: (context) => CertificatesCubit()..loadCertificates(),
          child: const CertificatesEditor(),
        );

      default:
        return BuildResponsiveDashboard();
    }
  }
}
