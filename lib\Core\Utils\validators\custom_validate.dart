String? customValidate({
  required String value,
  String? titleCheck,
  bool trimWhitespace = true,
  int? minLength,
  int? maxLength,
  String? pattern,
  String? emptyMessageEn,
  String? minLengthMessageEn,
  String? maxLengthMessageEn,
  String? invalidPatternMessageEn,
  String? emptyMessageAr,
  String? minLengthMessageAr,
  String? maxLengthMessageAr,
  String? invalidPatternMessageAr,
  bool isRtl = false,
}) {
  // Assigning default field name
  final String p0 = titleCheck ?? (isRtl ? "الحقل" : "Field");

  // Trim leading/trailing whitespace if trimWhitespace is true
  final String trimmedValue = trimWhitespace ? value.trim() : value;

  // Check if value is empty
  if (minLength == null && trimmedValue.isEmpty) {
    return isRtl ? emptyMessageAr ?? "الرجاء إدخال هذا الحقل." : emptyMessageEn ?? "Please enter this $p0.";
  }

  // Check for minimum length
  if (minLength != null && trimmedValue.length < minLength) {
    return isRtl ? minLengthMessageAr ?? "يجب أن يكون هذا الحقل على الأقل $minLength حرفا." : minLengthMessageEn ?? "$p0 must be at least $minLength characters long.";
  }

  // Check for maximum length
  if (maxLength != null && trimmedValue.length > maxLength) {
    return isRtl ? maxLengthMessageAr ?? "يجب أن يكون هذا الحقل على الأكثر $maxLength حرفا." : maxLengthMessageEn ?? "$p0 must be at most $maxLength characters long.";
  }

  // Check for pattern (regex) validation
  if (pattern != null) {
    final regex = RegExp(pattern);
    if (!regex.hasMatch(trimmedValue)) {
      return isRtl ? invalidPatternMessageAr ?? "صيغة الحقل غير صالحة." : invalidPatternMessageEn ?? "$p0 format is invalid.";
    }
  }

  // If all checks pass, return null (no errors)
  return null;
}