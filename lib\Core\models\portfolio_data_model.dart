import 'package:devfolio/Core/models/certification_model.dart';
import 'package:devfolio/Core/models/education_model.dart';
import 'package:devfolio/Core/models/experience_model.dart';
import 'package:devfolio/Core/models/personal_info_model.dart';
import 'package:devfolio/Core/models/project_model.dart';
import 'package:devfolio/Core/models/skill_model.dart';

class PortfolioDataModel {
  final String userId;
  final String email;
  final String phone;
  final String name;
  final String password;
  final PersonalInfoModel? personalInfo;
  final List<SkillModel>? skills;
  final List<ExperienceModel>? experience;
  final List<ProjectModel>? projects;
  final List<EducationModel>? education;
  final List<CertificationModel>? certifications;
  final List<SendMessageModel>? sendMessage;

  PortfolioDataModel({
    required this.userId,
    required this.email,
    required this.phone,
    required this.name,
    required this.password,
    this.personalInfo,
    this.skills,
    this.experience,
    this.projects,
    this.education,
    this.certifications,
    this.sendMessage,
  });

  factory PortfolioDataModel.fromJson(Map<String, dynamic> json) {
    return PortfolioDataModel(
      userId: json['userId']?.toString() ?? '',
      email: json['email']?.toString() ?? json['emailUser']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      password: json['password']?.toString() ?? '',
      personalInfo: json['personalInfo'] != null && json['personalInfo'] is Map
          ? PersonalInfoModel.fromJson(
              Map<String, dynamic>.from(json['personalInfo'] as Map),
            )
          : null,
      skills: (json['skills'] as List?)
          ?.map((e) => SkillModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      experience: (json['experience'] as List?)
          ?.map(
            (e) =>
                ExperienceModel.fromJson(Map<String, dynamic>.from(e as Map)),
          )
          .toList(),
      projects: (json['projects'] as List?)
          ?.map(
            (e) => ProjectModel.fromJson(Map<String, dynamic>.from(e as Map)),
          )
          .toList(),
      education: (json['education'] as List?)
          ?.map(
            (e) => EducationModel.fromJson(Map<String, dynamic>.from(e as Map)),
          )
          .toList(),
      certifications: (json['certifications'] as List?)
          ?.map(
            (e) => CertificationModel.fromJson(
              Map<String, dynamic>.from(e as Map),
            ),
          )
          .toList(),
      sendMessage: (json['sendMessage'] as List?)
          ?.map(
            (e) =>
                SendMessageModel.fromJson(Map<String, dynamic>.from(e as Map)),
          )
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'emailUser': email,
      'phone': phone,
      'name': name,
      'password': password,
      'personalInfo': personalInfo?.toJson() ?? {},
      'skills': skills?.map((e) => e.toJson()).toList() ?? [],
      'experience': experience?.map((e) => e.toJson()).toList() ?? [],
      'projects': projects?.map((e) => e.toJson()).toList() ?? [],
      'education': education?.map((e) => e.toJson()).toList() ?? [],
      'certifications': certifications?.map((e) => e.toJson()).toList() ?? [],
      'sendMessage': sendMessage?.map((e) => e.toJson()).toList() ?? [],
    };
  }
}

class SendMessageModel {
  final String? email;
  final String? message;
  final String? name;
  final String? dateTime;

  SendMessageModel({this.email, this.message, this.name, this.dateTime});

  factory SendMessageModel.fromJson(Map<String, dynamic> json) {
    return SendMessageModel(
      email: json['email'],
      message: json['message'],
      name: json['name'],
      dateTime: json['dateTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (email != null) 'email': email,
      if (message != null) 'message': message,
      if (name != null) 'name': name,
      if (dateTime != null) 'dateTime': dateTime,
    };
  }
}
