import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../../Core/Utils/Reusable/admin_nav_item.dart';
import '../../../../../Core/Utils/Reusable/admin_sidebar_header.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../config/Routes/route_names.dart';
import '../../../../../config/cubit/admin_cubit.dart';
import '../../../../../main.dart';

class BuildSlideBarDashBoard extends StatelessWidget {
  const BuildSlideBarDashBoard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320.w,
      decoration: BoxDecoration(
        color: AppColors.surface.withValues(alpha: 0.95),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          // Header with gradient
          AdminSidebarHeader(
            title: AppStrings.adminPanel,
            subtitle: AppStrings.portfolioManager,
            icon: Icons.admin_panel_settings,
          ),
          // Navigation items
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              child: Column(
                children: [
                  AdminNavItem(
                    icon: Icons.dashboard,
                    label: AppStrings.dashboard,
                    tooltip: AppStrings.dashboard,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.dashboardIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.dashboardIndex,
                    ),
                  ),
                  AdminNavItem(
                    icon: Icons.person,
                    label: AppStrings.personalInfo,
                    tooltip: AppStrings.personalInfoTooltip,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.personalInfoIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.personalInfoIndex,
                    ),
                  ),
                  AdminNavItem(
                    icon: Icons.psychology,
                    label: AppStrings.skills,
                    tooltip: AppStrings.skillsTooltip,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.skillsIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.skillsIndex,
                    ),
                  ),
                  AdminNavItem(
                    icon: Icons.folder,
                    label: AppStrings.projects,
                    tooltip: AppStrings.projectsTooltip,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.projectsIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.projectsIndex,
                    ),
                  ),
                  AdminNavItem(
                    icon: Icons.school,
                    label: AppStrings.education,
                    tooltip: AppStrings.educationTooltip,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.educationIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.educationIndex,
                    ),
                  ),
                  AdminNavItem(
                    icon: Icons.task_outlined,
                    label: AppStrings.certificates,
                    tooltip: AppStrings.certificatesTooltip,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.certificatesIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.certificatesIndex,
                    ),
                  ),
                  AdminNavItem(
                    icon: Icons.work,
                    label: AppStrings.experience,
                    tooltip: AppStrings.experienceTooltip,
                    isSelected:
                        context.watch<AdminCubit>().state.selectedSection ==
                        AppConstants.experienceIndex,
                    onTap: () => context.read<AdminCubit>().setSelectedSection(
                      AppConstants.experienceIndex,
                    ),
                  ),
                  // AdminNavItem(
                  //   icon: Icons.contact_mail,
                  //   label: AppStrings.contact,
                  //   tooltip: AppStrings.contactTooltip,
                  //   isSelected:
                  //       context.watch<AdminCubit>().state.selectedSection ==
                  //       AppConstants.contactIndex,
                  //       onTap: () => context.read<AdminCubit>().setSelectedSection(
                  //     AppConstants.contactIndex,
                  //   ),
                  // ),
                ],
              ),
            ),
          ),

          Container(
            margin: EdgeInsets.all(15.w),
            child: Column(
              children: [
                _buildPortfolioButton(context),
                SizedBox(height: 10.h),
                _buildLogoutButton(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPortfolioButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => _navigateToPortfolio(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textPrimary,
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
        elevation: 2,
      ),
      icon: Icon(Icons.visibility, size: 20.sp, color: AppColors.textPrimary),
      label: Text(
        AppStrings.viewPortfolio,
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  void _navigateToPortfolio(BuildContext context) {
    // Navigate to the main portfolio app using the navigation service
    kNavigationService.navigateToPortfolio();
  }

  Widget _buildLogoutButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => _showLogoutDialog(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.error,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
        elevation: 2,
      ),
      icon: Icon(Icons.logout, size: 20.sp, color: Colors.white),
      label: Text(
        'Logout',
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          title: Text(
            'Logout',
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: TextStyle(color: AppColors.textSecondary, fontSize: 14.sp),
          ),
          actions: [
            TextButton(
              onPressed: () => context.pop(),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14.sp,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                context.pop();
                kNavigationService.goToNamed(RouteNames.loginName);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                'Logout',
                style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }
}
