import 'package:flutter_test/flutter_test.dart';
import 'package:devfolio/Core/models/project_model.dart';
import 'package:devfolio/Features/Admin/projects/presentation/Cubit/projects_state.dart';

void main() {
  group('ProjectModel Tests', () {
    test('should create ProjectModel with all fields', () {
      final project = ProjectModel(
        id: '1',
        title: 'Test Project',
        description: 'Test Description',
        image: 'test_image.jpg',
        github: 'https://github.com/test',
        liveUrl: 'https://test.com',
        category: 'Mobile App',
        technologies: ['Flutter', 'Dart'],
      );

      expect(project.id, '1');
      expect(project.title, 'Test Project');
      expect(project.description, 'Test Description');
      expect(project.image, 'test_image.jpg');
      expect(project.github, 'https://github.com/test');
      expect(project.liveUrl, 'https://test.com');
      expect(project.category, 'Mobile App');
      expect(project.technologies, ['Flutter', 'Dart']);
    });

    test('should create ProjectModel from JSON with Supabase format', () {
      final json = {
        'id': '1',
        'title': 'Test Project',
        'description': 'Test Description',
        'image': 'test_image.jpg',
        'github_url': 'https://github.com/test', // Supabase format
        'live_url': 'https://test.com', // Supabase format
        'category': 'Mobile App',
        'technologies': ['Flutter', 'Dart'],
      };

      final project = ProjectModel.fromJson(json);

      expect(project.id, '1');
      expect(project.title, 'Test Project');
      expect(project.github, 'https://github.com/test');
      expect(project.liveUrl, 'https://test.com');
    });

    test('should create ProjectModel from JSON with local format', () {
      final json = {
        'id': '1',
        'title': 'Test Project',
        'description': 'Test Description',
        'image': 'test_image.jpg',
        'github': 'https://github.com/test', // Local format
        'liveUrl': 'https://test.com', // Local format
        'category': 'Mobile App',
        'technologies': ['Flutter', 'Dart'],
      };

      final project = ProjectModel.fromJson(json);

      expect(project.id, '1');
      expect(project.title, 'Test Project');
      expect(project.github, 'https://github.com/test');
      expect(project.liveUrl, 'https://test.com');
    });

    test('should convert ProjectModel to JSON', () {
      final project = ProjectModel(
        id: '1',
        title: 'Test Project',
        description: 'Test Description',
        image: 'test_image.jpg',
        github: 'https://github.com/test',
        liveUrl: 'https://test.com',
        category: 'Mobile App',
        technologies: ['Flutter', 'Dart'],
      );

      final json = project.toJson();

      expect(json['id'], '1');
      expect(json['title'], 'Test Project');
      expect(json['description'], 'Test Description');
      expect(json['image'], 'test_image.jpg');
      expect(json['github'], 'https://github.com/test');
      expect(json['liveUrl'], 'https://test.com');
      expect(json['category'], 'Mobile App');
      expect(json['technologies'], ['Flutter', 'Dart']);
    });

    test('should create copy with updated fields', () {
      final project = ProjectModel(
        id: '1',
        title: 'Test Project',
        description: 'Test Description',
      );

      final updatedProject = project.copyWith(
        title: 'Updated Project',
        category: 'Web App',
      );

      expect(updatedProject.id, '1');
      expect(updatedProject.title, 'Updated Project');
      expect(updatedProject.description, 'Test Description');
      expect(updatedProject.category, 'Web App');
    });

    test('should generate time-based ID', () async {
      final id1 = ProjectModel.generateTimeIdString();
      await Future.delayed(
        Duration(milliseconds: 1),
      ); // Ensure different timestamps
      final id2 = ProjectModel.generateTimeIdString();

      expect(id1, isNotEmpty);
      expect(id2, isNotEmpty);
      expect(id1, isNot(equals(id2)));
    });
  });

  group('ProjectsState Tests', () {
    test('should create initial state', () {
      final state = ProjectsState();

      expect(state.projectsList, isEmpty);
      expect(state.editingProject, isNull);
      expect(state.isAddingNew, false);
      expect(state.isLoading, false);
      expect(state.errorMessage, isNull);
      expect(state.hasChanges, false);
      expect(state.isSuccess, isNull);
      expect(state.isUploadingImage, false);
    });

    test('should copy state with updated fields', () {
      final initialState = ProjectsState();
      final project = ProjectModel(id: '1', title: 'Test');

      final updatedState = initialState.copyWith(
        projectsList: [project],
        isLoading: true,
        errorMessage: 'Test error',
        isSuccess: true,
        isUploadingImage: true,
      );

      expect(updatedState.projectsList, [project]);
      expect(updatedState.isLoading, true);
      expect(updatedState.errorMessage, 'Test error');
      expect(updatedState.isSuccess, true);
      expect(updatedState.isUploadingImage, true);
    });
  });
}
