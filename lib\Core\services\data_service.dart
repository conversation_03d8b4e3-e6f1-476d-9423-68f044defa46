import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';

class DataService {
  static const String _dataPath = 'lib/Core/Storage/portfolio_data.json';

  // Load data from JSON file
  static Future<Map<String, dynamic>> loadPortfolioData() async {
    try {
      final String jsonString = await rootBundle.loadString(_dataPath);
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return _getDefaultData();
    }
  }

  // Save data to JSON file
  static Future<bool> savePortfolioData(Map<String, dynamic> data) async {
    try {
      final String jsonString = json.encode(data);
      final File file = File(_dataPath);
      await file.writeAsString(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get personal info
  static Future<Map<String, dynamic>> getPersonalInfo() async {
    final data = await loadPortfolioData();
    return data['personalInfo'] ?? {};
  }

  // Update personal info
  static Future<bool> updatePersonalInfo(
    Map<String, dynamic> personalInfo,
  ) async {
    final data = await loadPortfolioData();
    data['personalInfo'] = personalInfo;
    return await savePortfolioData(data);
  }

  // Get skills
  static Future<List<Map<String, dynamic>>> getSkills() async {
    final data = await loadPortfolioData();
    final List<dynamic> skillsList = data['skills'] ?? [];
    return skillsList.map((skill) => skill as Map<String, dynamic>).toList();
  }

  // Add skill
  static Future<bool> addSkill(Map<String, dynamic> skill) async {
    final data = await loadPortfolioData();
    final List<dynamic> skills = data['skills'] ?? [];

    // Generate new ID
    final int newId = skills.isEmpty
        ? 1
        : skills
                  .map((s) => int.parse(s['id'].toString()))
                  .reduce((a, b) => a > b ? a : b) +
              1;
    skill['id'] = newId.toString();

    skills.add(skill);
    data['skills'] = skills;
    return await savePortfolioData(data);
  }

  // Update skill
  static Future<bool> updateSkill(String id, Map<String, dynamic> skill) async {
    final data = await loadPortfolioData();
    final List<dynamic> skills = data['skills'] ?? [];

    final int index = skills.indexWhere((s) => s['id'] == id);
    if (index != -1) {
      skill['id'] = id;
      skills[index] = skill;
      data['skills'] = skills;
      return await savePortfolioData(data);
    }
    return false;
  }

  // Delete skill
  static Future<bool> deleteSkill(String id) async {
    final data = await loadPortfolioData();
    final List<dynamic> skills = data['skills'] ?? [];

    skills.removeWhere((skill) => skill['id'] == id);
    data['skills'] = skills;
    return await savePortfolioData(data);
  }

  // Get experience
  static Future<List<Map<String, dynamic>>> getExperience() async {
    final data = await loadPortfolioData();
    final List<dynamic> experienceList = data['experience'] ?? [];
    return experienceList.map((exp) => exp as Map<String, dynamic>).toList();
  }

  // Add experience
  static Future<bool> addExperience(Map<String, dynamic> experience) async {
    final data = await loadPortfolioData();
    final List<dynamic> experiences = data['experience'] ?? [];

    // Generate new ID
    final int newId = experiences.isEmpty
        ? 1
        : experiences
                  .map((e) => int.parse(e['id'].toString()))
                  .reduce((a, b) => a > b ? a : b) +
              1;
    experience['id'] = newId.toString();

    experiences.add(experience);
    data['experience'] = experiences;
    return await savePortfolioData(data);
  }

  // Update experience
  static Future<bool> updateExperience(
    String id,
    Map<String, dynamic> experience,
  ) async {
    final data = await loadPortfolioData();
    final List<dynamic> experiences = data['experience'] ?? [];

    final int index = experiences.indexWhere((e) => e['id'] == id);
    if (index != -1) {
      experience['id'] = id;
      experiences[index] = experience;
      data['experience'] = experiences;
      return await savePortfolioData(data);
    }
    return false;
  }

  // Delete experience
  static Future<bool> deleteExperience(String id) async {
    final data = await loadPortfolioData();
    final List<dynamic> experiences = data['experience'] ?? [];

    experiences.removeWhere((exp) => exp['id'] == id);
    data['experience'] = experiences;
    return await savePortfolioData(data);
  }

  // Get projects
  static Future<List<Map<String, dynamic>>> getProjects() async {
    final data = await loadPortfolioData();
    final List<dynamic> projectsList = data['projects'] ?? [];
    return projectsList
        .map((project) => project as Map<String, dynamic>)
        .toList();
  }

  // Add project
  static Future<bool> addProject(Map<String, dynamic> project) async {
    final data = await loadPortfolioData();
    final List<dynamic> projects = data['projects'] ?? [];

    // Generate new ID
    final int newId = projects.isEmpty
        ? 1
        : projects
                  .map((p) => int.parse(p['id'].toString()))
                  .reduce((a, b) => a > b ? a : b) +
              1;
    project['id'] = newId.toString();

    projects.add(project);
    data['projects'] = projects;
    return await savePortfolioData(data);
  }

  // Update project
  static Future<bool> updateProject(
    String id,
    Map<String, dynamic> project,
  ) async {
    final data = await loadPortfolioData();
    final List<dynamic> projects = data['projects'] ?? [];

    final int index = projects.indexWhere((p) => p['id'] == id);
    if (index != -1) {
      project['id'] = id;
      projects[index] = project;
      data['projects'] = projects;
      return await savePortfolioData(data);
    }
    return false;
  }

  // Delete project
  static Future<bool> deleteProject(String id) async {
    final data = await loadPortfolioData();
    final List<dynamic> projects = data['projects'] ?? [];

    projects.removeWhere((project) => project['id'] == id);
    data['projects'] = projects;
    return await savePortfolioData(data);
  }

  // Get education
  static Future<List<Map<String, dynamic>>> getEducation() async {
    final data = await loadPortfolioData();
    final List<dynamic> educationList = data['education'] ?? [];
    return educationList.map((edu) => edu as Map<String, dynamic>).toList();
  }

  // Add education
  static Future<bool> addEducation(Map<String, dynamic> education) async {
    final data = await loadPortfolioData();
    final List<dynamic> educations = data['education'] ?? [];

    // Generate new ID
    final int newId = educations.isEmpty
        ? 1
        : educations
                  .map((e) => int.parse(e['id'].toString()))
                  .reduce((a, b) => a > b ? a : b) +
              1;
    education['id'] = newId.toString();

    educations.add(education);
    data['education'] = educations;
    return await savePortfolioData(data);
  }

  // Update education
  static Future<bool> updateEducation(
    String id,
    Map<String, dynamic> education,
  ) async {
    final data = await loadPortfolioData();
    final List<dynamic> educations = data['education'] ?? [];

    final int index = educations.indexWhere((e) => e['id'] == id);
    if (index != -1) {
      education['id'] = id;
      educations[index] = education;
      data['education'] = educations;
      return await savePortfolioData(data);
    }
    return false;
  }

  // Delete education
  static Future<bool> deleteEducation(String id) async {
    final data = await loadPortfolioData();
    final List<dynamic> educations = data['education'] ?? [];

    educations.removeWhere((edu) => edu['id'] == id);
    data['education'] = educations;
    return await savePortfolioData(data);
  }

  // Get contact info
  static Future<Map<String, dynamic>> getContactInfo() async {
    final data = await loadPortfolioData();
    return data['contact'] ?? {};
  }

  // Update contact info
  static Future<bool> updateContactInfo(
    Map<String, dynamic> contactInfo,
  ) async {
    final data = await loadPortfolioData();
    data['contact'] = contactInfo;
    return await savePortfolioData(data);
  }

  // Default data structure
  static Map<String, dynamic> _getDefaultData() {
    return {
      "personalInfo": {
        "name": "Your Name",
        "title": "Flutter Developer",
        "description":
            "Passionate Flutter developer with experience in building beautiful and responsive applications.",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "location": "Your City, Country",
        "github": "https://github.com/yourusername",
        "linkedin": "https://linkedin.com/in/yourusername",
        "website": "https://yourwebsite.com",
        "avatar": "assets/images/avatar.jpg",
        "about":
            "I am a dedicated Flutter developer with a passion for creating beautiful, responsive, and user-friendly applications.",
      },
      "skills": [
        {
          "id": "1",
          "name": "Flutter",
          "level": "Advanced",
          "category": "Mobile Development",
          "description": "Cross-platform mobile app development framework",
          "icon": "assets/icons/flutter.png",
        },
        {
          "id": "2",
          "name": "Dart",
          "level": "Advanced",
          "category": "Programming Languages",
          "description":
              "Object-oriented programming language for Flutter development",
          "icon": "assets/icons/dart.png",
        },
        {
          "id": "3",
          "name": "Firebase",
          "level": "Intermediate",
          "category": "Backend Services",
          "description":
              "Backend-as-a-Service for authentication, database, and hosting",
          "icon": "assets/icons/firebase.png",
        },
        {
          "id": "4",
          "name": "Git",
          "level": "Advanced",
          "category": "Version Control",
          "description":
              "Distributed version control system for code management",
          "icon": "assets/icons/git.png",
        },
        {
          "id": "5",
          "name": "REST APIs",
          "level": "Intermediate",
          "category": "Web Services",
          "description": "Integration with RESTful web services and APIs",
          "icon": "assets/icons/api.png",
        },
        {
          "id": "6",
          "name": "State Management",
          "level": "Advanced",
          "category": "Architecture",
          "description": "Bloc, Provider, and other state management solutions",
          "icon": "assets/icons/state.png",
        },
        {
          "id": "7",
          "name": "UI/UX Design",
          "level": "Intermediate",
          "category": "Design",
          "description": "Creating beautiful and user-friendly interfaces",
          "icon": "assets/icons/design.png",
        },
        {
          "id": "8",
          "name": "Testing",
          "level": "Intermediate",
          "category": "Quality Assurance",
          "description":
              "Unit testing, widget testing, and integration testing",
          "icon": "assets/icons/testing.png",
        },
      ],
      "experience": [
        {
          "id": "1",
          "company": "Tech Company",
          "position": "Senior Flutter Developer",
          "startDate": "2023-01",
          "endDate": "Present",
          "description":
              "Led development of multiple Flutter applications, mentored junior developers, and implemented best practices.",
          "technologies": ["Flutter", "Dart", "Firebase", "Git"],
          "location": "Remote",
        },
      ],
      "projects": [
        {
          "id": "1",
          "title": "E-Commerce App",
          "description":
              "A full-featured e-commerce mobile application with payment integration and admin panel.",
          "image": "assets/images/project1.jpg",
          "technologies": ["Flutter", "Firebase", "Stripe"],
          "github": "https://github.com/yourusername/ecommerce-app",
          "liveUrl": "https://ecommerce-app.com",
          "category": "Mobile App",
        },
      ],
      "education": [
        {
          "id": "1",
          "degree": "Bachelor of Computer Science",
          "institution": "University Name",
          "startDate": "2018-09",
          "endDate": "2022-06",
          "description":
              "Studied computer science with focus on software development and mobile applications.",
          "location": "City, Country",
          "gpa": "3.8",
        },
      ],
      "contact": {
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "address": "Your Address, City, Country",
        "github": "https://github.com/yourusername",
        "linkedin": "https://linkedin.com/in/yourusername",
        "twitter": "https://twitter.com/yourusername",
        "website": "https://yourwebsite.com",
      },
    };
  }
}
