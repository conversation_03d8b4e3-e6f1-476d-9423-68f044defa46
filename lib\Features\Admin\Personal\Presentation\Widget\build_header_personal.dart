import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildHeaderPersonal extends StatelessWidget {
  const BuildHeaderPersonal({super.key});



  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.withOpacity(AppColors.primary, 0.1),
            AppColors.withOpacity(AppColors.primaryVariant, 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.withOpacity(AppColors.primary, 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              Icons.person,
              color: AppColors.textPrimary,
              size: ResponsiveLayout.getLargeIconSize(context),
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: AppStrings.personalContactInfo,
                  fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                  fontWeight: FontWeight.bold,
                ),
                CustomText(
                  text: AppStrings.managePersonalContact,
                  fontSize: ResponsiveLayout.getSmallFontSize(context),
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
