class CertificationModel {
  final String? id;
  final String? name;
  final String? description;
  final String? image;
  final String? link;
  final String? dateTime;

  CertificationModel({
    this.id,
    this.name,
    this.description,
    this.image,
    this.link,
    this.dateTime,
  });

  factory CertificationModel.fromJson(Map<String, dynamic> json) {
    return CertificationModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      image: json['image'],
      link: json['link'],
      dateTime: json['dateTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (image != null) 'image': image,
      if (link != null) 'link': link,
      if (dateTime != null) 'dateTime': dateTime,
    };
  }
}
