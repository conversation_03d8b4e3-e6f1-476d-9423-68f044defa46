import 'package:devfolio/Core/Utils/Reusable/custom_card.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/skill_chip.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProjectCard extends StatelessWidget {
  final String title;
  final String description;
  final String? imageUrl;
  final List<String> technologies;
  final VoidCallback? onTap;

  const ProjectCard({
    super.key,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.technologies,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CustomCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (imageUrl != null) ...[
              ClipRRect(
                borderRadius: BorderRadius.circular(
                  ResponsiveLayout.getBorderRadius(context),
                ),
                child: Image.network(
                  imageUrl!,
                  width: double.infinity,
                  height: 200.h,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),
            ],
            CustomText(
              text: title,
              fontSize: ResponsiveLayout.getSubtitleFontSize(context),
              fontWeight: FontWeight.bold,
            ),
            SizedBox(height: ResponsiveLayout.getSmallSpacing(context)),
            CustomText(
              text: description,
              fontSize: ResponsiveLayout.getSmallFontSize(context),
              color: Colors.grey,
              maxLines: 3,
            ),
            SizedBox(height: ResponsiveLayout.getSectionSpacing(context)),
            Wrap(
              spacing: ResponsiveLayout.getSmallSpacing(context),
              runSpacing: ResponsiveLayout.getSmallSpacing(context),
              children: technologies
                  .map((tech) => SkillChip(skill: tech))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }
}
