import 'package:devfolio/Core/Utils/Reusable/custom_button.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';

class EducationHeader extends StatelessWidget {
  final VoidCallback onAddPressed;
  final VoidCallback onRefreshPressed;

  const EducationHeader({
    super.key,
    required this.onAddPressed,
    required this.onRefreshPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Title
        CustomText(
          text: 'Education Management',
          fontSize: ResponsiveLayout.getSmallFontSize(context),
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        
        // Action buttons
        Row(
          children: [
            // Refresh button
            IconButton(
              onPressed: onRefreshPressed,
              icon: Icon(
                Icons.refresh,
                color: AppColors.primary,
                size: 24.sp,
              ),
              tooltip: 'Refresh',
            ),
            SizedBox(width: 10.w),
            
            // Add button
            CustomButton(
              text: 'Add Education',
              onPressed: onAddPressed,
              backgroundColor: AppColors.primary,
              textColor: Colors.white,
             
            ),
          ],
        ),
      ],
    );
  }
}
