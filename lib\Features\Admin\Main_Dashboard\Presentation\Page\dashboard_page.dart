import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/Utils/Reusable/gradient_container.dart';
import '../../../../../Core/Utils/widgets/build_header_dashboard.dart';
import '../../../../../Core/Utils/widgets/build_loading.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/resources/resources.dart';
import '../../../../../config/cubit/admin_cubit.dart';
import '../Widget/build_mobile_header_menue_dashboard.dart';
import '../Widget/build_slide_bar_dash_board.dart';

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdminCubit, AdminState>(
    
      builder: (context, state) {
        return state.isLoading
            ? buildLoading()
            : _AdminDashboardContent(state: state);
      },
    );
  }
}

class _AdminDashboardContent extends StatefulWidget {
  const _AdminDashboardContent({required this.state});
  final AdminState state;
  @override
  State<_AdminDashboardContent> createState() => _AdminDashboardContentState();
}

class _AdminDashboardContentState extends State<_AdminDashboardContent> {

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveLayout.isDesktop(context);
    return Scaffold(
      backgroundColor: AppColors.background,
      body: GradientContainer(
        child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        BuildMobileHeaderMenueDashboard(),
        Expanded(
          child: _buildMainContent(
            widget.state,
          ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.1),
        ),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Sidebar
        BuildSlideBarDashBoard()
            .animate()
            .fadeIn(duration: 800.ms)
            .slideY(begin: -0.1),
        // Main content
        Expanded(
          child: _buildMainContent(
            widget.state,
          ).animate().fadeIn(duration: 800.ms).slideX(begin: 0.1),
        ),
      ],
    );
  }

  Widget _buildMainContent(AdminState state) {
    return Container(
      padding: EdgeInsets.all(ResponsiveLayout.isMobile(context) ? 15.w : 30.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BuildHeaderDashboard(state: state),
          SizedBox(height: ResponsiveLayout.isMobile(context) ? 20.h : 30.h),
          Expanded(child: context.watch<AdminCubit>().buildContent()),
        ],
      ),
    );
  }
}
