import '../../../../../Core/Utils/Reusable/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/layout/responsive_layout.dart';
import '../../../../../Core/models/skill_model.dart';
import '../../../../../Core/resources/app_icon.dart';
import '../../../../../Core/resources/resources.dart';

class SkillCard extends StatelessWidget {
  final SkillModel skill;
  final bool isSelected;
  final VoidCallback onDelete;
  final VoidCallback onTap;

  const SkillCard({
    super.key,
    required this.skill,
    required this.isSelected,
    required this.onDelete,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.withOpacity(AppColors.primary, 0.1)
            : AppColors.cardSurface,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: isSelected
              ? AppColors.primary
              : AppColors.withOpacity(AppColors.textSecondary, 0.2),
        ),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
        leading: Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: AppColors.withOpacity(AppColors.primary, 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            AppIcon.getIconData(skill.icon ?? ''),
            color: AppColors.primary,
            size: 20.sp,
          ),
        ),
        title: CustomText(
          text: skill.name ?? '',
          fontSize: ResponsiveLayout.getBodyFontSize(context),
          fontWeight: FontWeight.w600,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            CustomText(
              text: skill.category ?? '',
              fontSize: ResponsiveLayout.getSmallFontSize(context),
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 6.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: AppColors.withOpacity(AppColors.primary, 0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: AppColors.withOpacity(AppColors.primary, 0.3),
                ),
              ),
              child: CustomText(
                text: skill.category ?? '',
                fontSize: 10.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(Icons.delete, color: AppColors.error, size: 20.sp),
              onPressed: onDelete,
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  
}
