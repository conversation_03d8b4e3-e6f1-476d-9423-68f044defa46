import 'package:devfolio/Core/resources/resources.dart';
import 'package:devfolio/Core/Utils/widgets/build_text_field.dart';
import 'package:devfolio/Features/Admin/Personal/Presentation/Widget/build_section_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildSocialMediaTab extends StatelessWidget {
  const BuildSocialMediaTab({
    super.key,
    required this.twitterController,
    required this.instagramController,
    required this.facebookController,
    required this.youtubeController,
  });

  final TextEditingController twitterController;
  final TextEditingController instagramController;
  final TextEditingController facebookController;
  final TextEditingController youtubeController;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BuildSectionTitle(title:AppStrings.socialMediaProfiles),
          Sized<PERSON>ox(height: 15.h),
          BuildText<PERSON>ield(
            controller: twitterController,
            label: AppStrings.twitter,
            icon: Icons.flutter_dash,
            keyboardType: TextInputType.url,
            hint: 'https://twitter.com/yourhandle',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: instagramController,
            label: AppStrings.instagram,
            icon: Icons.camera_alt,
            keyboardType: TextInputType.url,
            hint: 'https://instagram.com/yourhandle',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: facebookController,
            label: AppStrings.facebook,
            icon: Icons.facebook,
            keyboardType: TextInputType.url,
            hint: 'https://facebook.com/yourprofile',
          ),
          SizedBox(height: 15.h),
          BuildTextField(
            controller: youtubeController,
            label: AppStrings.youtube,
            icon: Icons.play_circle,
            keyboardType: TextInputType.url,
            hint: 'https://youtube.com/@yourchannel',
          ),
        ],
      ),
    );
  }
}
