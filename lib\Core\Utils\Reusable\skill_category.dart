import 'package:devfolio/Core/Utils/Reusable/custom_card.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_icon.dart';
import 'package:devfolio/Core/Utils/Reusable/custom_text.dart';
import 'package:devfolio/Core/Utils/Reusable/skill_chip.dart';
import 'package:devfolio/Core/layout/responsive_layout.dart';
import 'package:devfolio/Core/resources/resources.dart';
import 'package:flutter/material.dart';

class SkillCategory extends StatelessWidget {
  final String title;
  final List<String> skills;
  final IconData icon;

  const SkillCategory({
    super.key,
    required this.title,
    required this.skills,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIcon(
                icon: icon,
                size: ResponsiveLayout.getLargeIconSize(context),
                color: AppColors.primary,
              ),
              SizedBox(width: ResponsiveLayout.getMediumSpacing(context)),
              CustomText(
                text: title,
                fontSize: ResponsiveLayout.getSubtitleFontSize(context),
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
          SizedBox(height: ResponsiveLayout.getMediumSpacing(context)),
          Wrap(
            spacing: ResponsiveLayout.getSmallSpacing(context),
            runSpacing: ResponsiveLayout.getSmallSpacing(context),
            children: skills.map((skill) => SkillChip(skill: skill)).toList(),
          ),
        ],
      ),
    );
  }
}
