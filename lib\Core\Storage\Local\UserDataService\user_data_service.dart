import 'user_data_base_service.dart';
import 'user_data_list_extensions.dart';

class UserDataService with UserDataBaseService, UserDataListExtensions {
  static Future<void> init() async => await UserDataBaseService.init();

  static Future<bool> hasUserData(String userName) async {
    final email = UserDataBaseService.getEmailUser();
    if (email.isEmpty) {
      return false;
    }
    return email == userName;
  }

  static Future<void> saveUserData(Map<String, dynamic> userData) async =>
      await UserDataBaseService.saveUserData(userData);

  static Map<String, dynamic>? getUserData() =>
      UserDataBaseService.getUserData();

  static Future<void> updateUserData(Map<String, dynamic> updates) async =>
      await UserDataBaseService.updateUserData(updates);
  static String getEmailUser() => UserDataBaseService.getEmailUser();

  static List<Map<String, dynamic>> getListData(String key) =>
      UserDataListExtensions.list(key).get();

  static Future<void> updateListData(
    String key,
    List<Map<String, dynamic>> list,
  ) async => await UserDataListExtensions.list(key).updateAll(list);

  static Future<void> addItemToList(
    String key,
    Map<String, dynamic> item,
  ) async => await UserDataListExtensions.list(key).add(item);

  static Future<void> updateItemInList(
    String key,
    String itemId,
    Map<String, dynamic> updates,
  ) async => await UserDataListExtensions.list(key).update(itemId, updates);

  static Future<void> deleteItemFromList(String key, String itemId) async =>
      await UserDataListExtensions.list(key).delete(itemId);

  static Map<String, dynamic>? getMapField(String key) =>
      UserDataListExtensions.getMapField(key);

  static Future<void> updateMapField(
    String key,
    Map<String, dynamic> data,
  ) async => await UserDataListExtensions.updateMapField(key, data);

  static Future<void> deleteMapField(String key) async =>
      await UserDataListExtensions.updateMapField(key, {});
}
